/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.AddressDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalCustomerDetailsCustomerAddressStatusUpdateRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalCustomerDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalCustomerDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalCustomerShippingAddressesPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalCustomersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface CustomersApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/customers/{customer_key}/shipping-addresses"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun addCustomerShippingAddress( @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/customers"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createCustomer( @Valid @RequestBody externalCustomerDetailsDataDto: ExternalCustomerDetailsDataDto): ResponseEntity<ExternalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/customers/{customer_key}"],
            produces = ["application/json"]
    )
    suspend fun loadCustomer( @PathVariable("customer_key") customerKey: kotlin.String): ResponseEntity<ExternalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/customers/{customer_key}/shipping-addresses/{address_key}"],
            produces = ["application/json"]
    )
    suspend fun loadCustomerShippingAddress( @PathVariable("customer_key") customerKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/customers/{customer_key}/shipping-addresses"],
            produces = ["application/json"]
    )
    suspend fun loadCustomerShippingAddresses( @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalCustomerShippingAddressesPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/customers"],
            produces = ["application/json"]
    )
    suspend fun loadCustomers( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalCustomersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/customers/{customer_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomer( @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody externalCustomerDetailsDataDto: ExternalCustomerDetailsDataDto): ResponseEntity<ExternalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/customers/{customer_key}/shipping-addresses/{address_key}/status"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerAddressStatus( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String, @Valid @RequestBody externalCustomerDetailsCustomerAddressStatusUpdateRequestDto: ExternalCustomerDetailsCustomerAddressStatusUpdateRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/customers/{customer_key}/billing-address"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerBillingAddress( @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/customers/{customer_key}/shipping-addresses/{address_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerShippingAddress( @PathVariable("customer_key") customerKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>
}
