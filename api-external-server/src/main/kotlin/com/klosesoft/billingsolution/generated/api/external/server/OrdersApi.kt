/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrderItemRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrderItemResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrderItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrderRequestDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrderResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalOrdersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface OrdersApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/orders/{order_key}/order-items"],
            consumes = ["application/json"]
    )
    suspend fun addOrderItem( @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestBody externalOrderItemRequestDto: ExternalOrderItemRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/orders/{order_key}/cancel"]
    )
    suspend fun cancelOrder( @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/orders"],
            consumes = ["application/json"]
    )
    suspend fun createOrder( @Valid @RequestBody externalOrderRequestDto: ExternalOrderRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.DELETE],
            value = ["/external/v1/orders/{order_key}/order-items/{order_item_key}"]
    )
    suspend fun deleteOrderItem( @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("order_item_key") orderItemKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/orders/{order_key}/finalize"]
    )
    suspend fun finalizeOrder( @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/orders/{order_key}"],
            produces = ["application/json"]
    )
    suspend fun loadOrder( @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<ExternalOrderResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/orders/{order_key}/order-items/{order_item_key}"],
            produces = ["application/json"]
    )
    suspend fun loadOrderItem( @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("order_item_key") orderItemKey: kotlin.String): ResponseEntity<ExternalOrderItemResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/orders/{order_key}/order-items"],
            produces = ["application/json"]
    )
    suspend fun loadOrderItems( @PathVariable("order_key") orderKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "page", required = true) page: kotlin.Int,@NotNull  @Valid @RequestParam(value = "size", required = true) size: kotlin.Int, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalOrderItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/orders"],
            produces = ["application/json"]
    )
    suspend fun loadOrders(@NotNull  @Valid @RequestParam(value = "page", required = true) page: kotlin.Int,@NotNull  @Valid @RequestParam(value = "size", required = true) size: kotlin.Int, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalOrdersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/orders/{order_key}"],
            consumes = ["application/json"]
    )
    suspend fun updateOrder( @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestBody externalOrderRequestDto: ExternalOrderRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/orders/{order_key}/order-items/{order_item_key}"],
            consumes = ["application/json"]
    )
    suspend fun updateOrderItem( @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("order_item_key") orderItemKey: kotlin.String, @Valid @RequestBody externalOrderItemRequestDto: ExternalOrderItemRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PATCH],
            value = ["/external/v1/orders/{order_key}"],
            consumes = ["application/json"]
    )
    suspend fun updateOrderPartial( @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestBody externalOrderRequestDto: ExternalOrderRequestDto): ResponseEntity<Unit>
}
