/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.AddressDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSupplierDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSupplierDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSuppliersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface SuppliersApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/suppliers"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createSupplier( @Valid @RequestBody externalSupplierDetailsDataDto: ExternalSupplierDetailsDataDto): ResponseEntity<ExternalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/suppliers/{supplier_key}"],
            produces = ["application/json"]
    )
    suspend fun loadSupplier( @PathVariable("supplier_key") supplierKey: kotlin.String): ResponseEntity<ExternalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/suppliers/{supplier_key}/billing-address"],
            produces = ["application/json"]
    )
    suspend fun loadSupplierBillingAddress( @PathVariable("supplier_key") supplierKey: kotlin.String): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/suppliers"],
            produces = ["application/json"]
    )
    suspend fun loadSuppliers( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalSuppliersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/suppliers/{supplier_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateSupplier( @PathVariable("supplier_key") supplierKey: kotlin.String, @Valid @RequestBody externalSupplierDetailsDataDto: ExternalSupplierDetailsDataDto): ResponseEntity<ExternalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/suppliers/{supplier_key}/billing-address"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateSupplierBillingAddress( @PathVariable("supplier_key") supplierKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>
}
