/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.DocumentFormatDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface DocumentsApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/orders/{order_key}/documents/{document_key}/file"],
            produces = ["application/*"]
    )
    suspend fun loadDocumentFile( @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("document_key") documentKey: kotlin.String, @Valid @RequestParam(value = "format", required = false) format: DocumentFormatDto?): ResponseEntity<org.springframework.core.io.Resource>
}
