/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPostingRecordDetailsDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPostingRecordEntriesPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPostingRecordsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface PostingRecordsApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/posting-records/{posting_record_key}"],
            produces = ["application/json"]
    )
    suspend fun loadPostingRecord( @PathVariable("posting_record_key") postingRecordKey: kotlin.String): ResponseEntity<ExternalPostingRecordDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/posting-records/{posting_record_key}/entries"],
            produces = ["application/json"]
    )
    suspend fun loadPostingRecordEntries( @PathVariable("posting_record_key") postingRecordKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalPostingRecordEntriesPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/posting-records"],
            produces = ["application/json"]
    )
    suspend fun loadPostingRecords( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalPostingRecordsPagedResponseDto>
}
