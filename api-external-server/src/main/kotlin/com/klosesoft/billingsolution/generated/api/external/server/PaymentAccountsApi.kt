/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPaymentAccountDetailsDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPaymentAccountsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface PaymentAccountsApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/payment-accounts"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createPaymentAccount( @Valid @RequestBody externalPaymentAccountDetailsDto: ExternalPaymentAccountDetailsDto): ResponseEntity<ExternalPaymentAccountDetailsDto>


    @RequestMapping(
            method = [RequestMethod.DELETE],
            value = ["/external/v1/payment-accounts/{payment_account_key}"]
    )
    suspend fun deletePaymentAccount( @PathVariable("payment_account_key") paymentAccountKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/payment-accounts/{payment_account_key}"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentAccount( @PathVariable("payment_account_key") paymentAccountKey: kotlin.String): ResponseEntity<ExternalPaymentAccountDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/payment-accounts"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentAccounts( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalPaymentAccountsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/external/v1/payment-accounts/{payment_account_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updatePaymentAccount( @PathVariable("payment_account_key") paymentAccountKey: kotlin.String, @Valid @RequestBody externalPaymentAccountDetailsDto: ExternalPaymentAccountDetailsDto): ResponseEntity<ExternalPaymentAccountDetailsDto>
}
