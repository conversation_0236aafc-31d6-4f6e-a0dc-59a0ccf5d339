/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPaymentAssignmentDetailsDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalPaymentAssignmentsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface PaymentAssignmentsApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/payment-assignments"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createPaymentAssignment( @Valid @RequestBody externalPaymentAssignmentDetailsDto: ExternalPaymentAssignmentDetailsDto): ResponseEntity<ExternalPaymentAssignmentDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/payment-assignments/{payment_assignment_key}"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentAssignment( @PathVariable("payment_assignment_key") paymentAssignmentKey: kotlin.String): ResponseEntity<ExternalPaymentAssignmentDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/payment-assignments"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentAssignments( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalPaymentAssignmentsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/payment-assignments/{payment_assignment_key}/reverse"]
    )
    suspend fun reversePaymentAssignment( @PathVariable("payment_assignment_key") paymentAssignmentKey: kotlin.String): ResponseEntity<Unit>
}
