/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.external.server

import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSettlementReportDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSettlementReportDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSettlementReportItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSettlementReportsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface SettlementReportsApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/settlement-reports"],
            consumes = ["application/json"]
    )
    suspend fun createSettlementReport( @Valid @RequestBody externalSettlementReportDto: ExternalSettlementReportDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/settlement-reports/{settlement_report_key}"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReport( @PathVariable("settlement_report_key") settlementReportKey: kotlin.String): ResponseEntity<ExternalSettlementReportDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/settlement-reports/{settlement_report_key}/settlement-report-items"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReportItems( @PathVariable("settlement_report_key") settlementReportKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalSettlementReportItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/external/v1/settlement-reports"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReports( @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<ExternalSettlementReportsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/external/v1/settlement-report-files"],
            consumes = ["multipart/form-data"]
    )
    suspend fun uploadSettlementReportFile( @Valid @RequestPart("report", required = true) report: org.springframework.core.io.Resource): ResponseEntity<Unit>
}
