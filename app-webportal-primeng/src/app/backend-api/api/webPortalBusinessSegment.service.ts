/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { WebPortalBusinessSegmentDetails } from '../model/webPortalBusinessSegmentDetails';
// @ts-ignore
import { WebPortalBusinessSegmentsPagedResponse } from '../model/webPortalBusinessSegmentsPagedResponse';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';



@Injectable({
  providedIn: 'root'
})
export class WebPortalBusinessSegmentService {

    protected basePath = 'http://localhost:8080';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();
    public encoder: HttpParameterCodec;

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string|string[], @Optional() configuration: Configuration) {
        if (configuration) {
            this.configuration = configuration;
        }
        if (typeof this.configuration.basePath !== 'string') {
            const firstBasePath = Array.isArray(basePath) ? basePath[0] : undefined;
            if (firstBasePath != undefined) {
                basePath = firstBasePath;
            }

            if (typeof basePath !== 'string') {
                basePath = this.basePath;
            }
            this.configuration.basePath = basePath;
        }
        this.encoder = this.configuration.encoder || new CustomHttpParameterCodec();
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data';
        for (const consume of consumes) {
            if (form === consume) {
                return true;
            }
        }
        return false;
    }

    // @ts-ignore
    private addToHttpParams(httpParams: HttpParams, value: any, key?: string): HttpParams {
        if (typeof value === "object" && value instanceof Date === false) {
            httpParams = this.addToHttpParamsRecursive(httpParams, value);
        } else {
            httpParams = this.addToHttpParamsRecursive(httpParams, value, key);
        }
        return httpParams;
    }

    private addToHttpParamsRecursive(httpParams: HttpParams, value?: any, key?: string): HttpParams {
        if (value == null) {
            return httpParams;
        }

        if (typeof value === "object") {
            if (Array.isArray(value)) {
                (value as any[]).forEach( elem => httpParams = this.addToHttpParamsRecursive(httpParams, elem, key));
            } else if (value instanceof Date) {
                if (key != null) {
                    httpParams = httpParams.append(key, (value as Date).toISOString().substring(0, 10));
                } else {
                   throw Error("key may not be null if value is Date");
                }
            } else {
                Object.keys(value).forEach( k => httpParams = this.addToHttpParamsRecursive(
                    httpParams, value[k], key != null ? `${key}.${k}` : k));
            }
        } else if (key != null) {
            httpParams = httpParams.append(key, value);
        } else {
            throw Error("key may not be null if value is not object or array");
        }
        return httpParams;
    }

    /**
     * Create a new business segment
     * @param xTenantKey The key of the tenant
     * @param key 
     * @param invoiceNumberPattern 
     * @param vatId 
     * @param originatorAddressKey 
     * @param version 
     * @param logo 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createBusinessSegment(xTenantKey: string, key: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo: Blob, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalBusinessSegmentDetails>;
    public createBusinessSegment(xTenantKey: string, key: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo: Blob, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalBusinessSegmentDetails>>;
    public createBusinessSegment(xTenantKey: string, key: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo: Blob, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalBusinessSegmentDetails>>;
    public createBusinessSegment(xTenantKey: string, key: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo: Blob, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling createBusinessSegment.');
        }
        if (key === null || key === undefined) {
            throw new Error('Required parameter key was null or undefined when calling createBusinessSegment.');
        }
        if (invoiceNumberPattern === null || invoiceNumberPattern === undefined) {
            throw new Error('Required parameter invoiceNumberPattern was null or undefined when calling createBusinessSegment.');
        }
        if (vatId === null || vatId === undefined) {
            throw new Error('Required parameter vatId was null or undefined when calling createBusinessSegment.');
        }
        if (originatorAddressKey === null || originatorAddressKey === undefined) {
            throw new Error('Required parameter originatorAddressKey was null or undefined when calling createBusinessSegment.');
        }
        if (version === null || version === undefined) {
            throw new Error('Required parameter version was null or undefined when calling createBusinessSegment.');
        }
        if (logo === null || logo === undefined) {
            throw new Error('Required parameter logo was null or undefined when calling createBusinessSegment.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (invoiceNumberPattern !== undefined && invoiceNumberPattern !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>invoiceNumberPattern, 'invoiceNumberPattern');
        }
        if (vatId !== undefined && vatId !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>vatId, 'vatId');
        }
        if (originatorAddressKey !== undefined && originatorAddressKey !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>originatorAddressKey, 'originatorAddressKey');
        }
        if (version !== undefined && version !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>version, 'version');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (logo !== undefined) {
            localVarFormParams = localVarFormParams.append('logo', <any>logo) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/businesssegments`;
        return this.httpClient.request<WebPortalBusinessSegmentDetails>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load a specific business segment
     * @param xTenantKey The key of the tenant
     * @param segmentKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadBusinessSegment(xTenantKey: string, segmentKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalBusinessSegmentDetails>;
    public loadBusinessSegment(xTenantKey: string, segmentKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalBusinessSegmentDetails>>;
    public loadBusinessSegment(xTenantKey: string, segmentKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalBusinessSegmentDetails>>;
    public loadBusinessSegment(xTenantKey: string, segmentKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadBusinessSegment.');
        }
        if (segmentKey === null || segmentKey === undefined) {
            throw new Error('Required parameter segmentKey was null or undefined when calling loadBusinessSegment.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/businesssegments/${this.configuration.encodeParam({name: "segmentKey", value: segmentKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalBusinessSegmentDetails>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load all business segments
     * @param xTenantKey The key of the tenant
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the items by
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the business segments. This can be any attribute of the business segment.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadBusinessSegments(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalBusinessSegmentsPagedResponse>;
    public loadBusinessSegments(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalBusinessSegmentsPagedResponse>>;
    public loadBusinessSegments(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalBusinessSegmentsPagedResponse>>;
    public loadBusinessSegments(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadBusinessSegments.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/businesssegments`;
        return this.httpClient.request<WebPortalBusinessSegmentsPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a specific business segment
     * @param xTenantKey The key of the tenant
     * @param segmentKey 
     * @param invoiceNumberPattern 
     * @param vatId 
     * @param originatorAddressKey 
     * @param version 
     * @param logo 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateBusinessSegment(xTenantKey: string, segmentKey: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo?: Blob, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalBusinessSegmentDetails>;
    public updateBusinessSegment(xTenantKey: string, segmentKey: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo?: Blob, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalBusinessSegmentDetails>>;
    public updateBusinessSegment(xTenantKey: string, segmentKey: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo?: Blob, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalBusinessSegmentDetails>>;
    public updateBusinessSegment(xTenantKey: string, segmentKey: string, invoiceNumberPattern: string, vatId: string, originatorAddressKey: string, version: number, logo?: Blob, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateBusinessSegment.');
        }
        if (segmentKey === null || segmentKey === undefined) {
            throw new Error('Required parameter segmentKey was null or undefined when calling updateBusinessSegment.');
        }
        if (invoiceNumberPattern === null || invoiceNumberPattern === undefined) {
            throw new Error('Required parameter invoiceNumberPattern was null or undefined when calling updateBusinessSegment.');
        }
        if (vatId === null || vatId === undefined) {
            throw new Error('Required parameter vatId was null or undefined when calling updateBusinessSegment.');
        }
        if (originatorAddressKey === null || originatorAddressKey === undefined) {
            throw new Error('Required parameter originatorAddressKey was null or undefined when calling updateBusinessSegment.');
        }
        if (version === null || version === undefined) {
            throw new Error('Required parameter version was null or undefined when calling updateBusinessSegment.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (invoiceNumberPattern !== undefined && invoiceNumberPattern !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>invoiceNumberPattern, 'invoiceNumberPattern');
        }
        if (vatId !== undefined && vatId !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>vatId, 'vatId');
        }
        if (originatorAddressKey !== undefined && originatorAddressKey !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>originatorAddressKey, 'originatorAddressKey');
        }
        if (version !== undefined && version !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>version, 'version');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (logo !== undefined) {
            localVarFormParams = localVarFormParams.append('logo', <any>logo) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/businesssegments/${this.configuration.encodeParam({name: "segmentKey", value: segmentKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalBusinessSegmentDetails>('put', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
