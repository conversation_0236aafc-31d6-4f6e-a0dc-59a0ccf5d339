/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaxInformation } from './taxInformation';
import { ItemCategory } from './itemCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';
import { Currency } from './currency';


export interface WebPortalOrderTemplateItemRequest { 
    /**
     * The article number of the order item
     */
    articleNumber: string;
    /**
     * The item group of the order item
     */
    itemGroup?: string;
    category: ItemCategory;
    debitCreditIndicator: DebitCreditIndicator;
    /**
     * The name of the order item
     */
    name: string;
    /**
     * The description of the order item
     */
    description?: string;
    /**
     * The quantity of the order item
     */
    quantity: number;
    /**
     * The unit net amount of the order item
     */
    unitNetAmount: number;
    currency: Currency;
    taxInformation: TaxInformation;
}
export namespace WebPortalOrderTemplateItemRequest {
}


