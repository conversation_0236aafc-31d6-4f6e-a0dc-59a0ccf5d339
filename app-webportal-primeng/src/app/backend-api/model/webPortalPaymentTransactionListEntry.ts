/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PaymentCategory } from './paymentCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';
import { Currency } from './currency';
import { PaymentTransactionAssignmentStatus } from './paymentTransactionAssignmentStatus';


export interface WebPortalPaymentTransactionListEntry { 
    key: string;
    /**
     * The date of the booking
     */
    bookingDate: string;
    debitCreditIndicator: DebitCreditIndicator;
    assignmentStatus: PaymentTransactionAssignmentStatus;
    /**
     * The text of the payment transaction
     */
    text: string;
    /**
     * The amount of the payment transaction
     */
    amount: number;
    currency: Currency;
    paymentCategory: PaymentCategory;
}
export namespace WebPortalPaymentTransactionListEntry {
}


