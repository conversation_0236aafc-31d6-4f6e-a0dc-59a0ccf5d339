/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DebitCreditIndicator } from './debitCreditIndicator';
import { BalanceCaseItemType } from './balanceCaseItemType';
import { Currency } from './currency';


export interface WebPortalBalanceCaseItemsListEntry { 
    key: string;
    referenceKey?: string;
    postingDate: string;
    debitCreditIndicator: DebitCreditIndicator;
    type: BalanceCaseItemType;
    totalAmount: number;
    currency: Currency;
}
export namespace WebPortalBalanceCaseItemsListEntry {
}


