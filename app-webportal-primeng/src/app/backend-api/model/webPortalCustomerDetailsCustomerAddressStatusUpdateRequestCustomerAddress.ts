/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface WebPortalCustomerDetailsCustomerAddressStatusUpdateRequestCustomerAddress { 
    status?: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequestCustomerAddress.StatusEnum;
}
export namespace WebPortalCustomerDetailsCustomerAddressStatusUpdateRequestCustomerAddress {
    export type StatusEnum = 'ACTIVE' | 'INACTIVE';
    export const StatusEnum = {
        Active: 'ACTIVE' as StatusEnum,
        Inactive: 'INACTIVE' as StatusEnum
    };
}


