/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export type BalanceCaseItemType = 'PAY_IN' | 'PAY_IN_REVERSED' | 'PAY_OUT' | 'PAY_OUT_REVERSED' | 'DEPOSIT_INVOICE' | 'DEPOSIT_CREDIT_NOTE' | 'INVOICE' | 'CREDIT_NOTE';

export const BalanceCaseItemType = {
    PayIn: 'PAY_IN' as BalanceCaseItemType,
    PayInReversed: 'PAY_IN_REVERSED' as BalanceCaseItemType,
    PayOut: 'PAY_OUT' as BalanceCaseItemType,
    PayOutReversed: 'PAY_OUT_REVERSED' as BalanceCaseItemType,
    DepositInvoice: 'DEPOSIT_INVOICE' as BalanceCaseItemType,
    DepositCreditNote: 'DEPOSIT_CREDIT_NOTE' as BalanceCaseItemType,
    Invoice: 'INVOICE' as BalanceCaseItemType,
    CreditNote: 'CREDIT_NOTE' as BalanceCaseItemType
};

