/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { NotificationType } from './notificationType';


export interface WebPortalNotificationReceiverSubpathDetails { 
    subpath: string;
    /**
     * The notification types that the receiver is subscribed to
     */
    notificationTypes: Array<NotificationType>;
}

