/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PaymentCategory } from './paymentCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';


export interface WebPortalPaymentCategorizationRuleDetails { 
    name: string;
    debitCreditIndicator?: DebitCreditIndicator;
    senders?: string;
    receivers?: string;
    purposes?: string;
    targetPaymentCategory: PaymentCategory;
    key: string;
    version: number;
}
export namespace WebPortalPaymentCategorizationRuleDetails {
}


