/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';
import { PostingRecordTargetType } from './postingRecordTargetType';


export interface WebPortalOrderPostingRecordResponse { 
    /**
     * The key of the posting record
     */
    key: string;
    /**
     * The number of the posting record
     */
    number: number;
    targetType?: PostingRecordTargetType;
    /**
     * The booking date of the posting record
     */
    bookingDate: string;
    currency: Currency;
}
export namespace WebPortalOrderPostingRecordResponse {
}


