/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The type of the target
 */
export type PaymentAssignmentTargetType = 'ORDER' | 'DEPOSIT' | 'FINAL' | 'REVERSED' | 'SELFBILLING' | 'SELFBILLING_REVERSED' | 'COMMISSION' | 'COMMISSION_REVERSED';

export const PaymentAssignmentTargetType = {
    Order: 'ORDER' as PaymentAssignmentTargetType,
    Deposit: 'DEPOSIT' as PaymentAssignmentTargetType,
    Final: 'FINAL' as PaymentAssignmentTargetType,
    Reversed: 'REVERSED' as PaymentAssignmentTargetType,
    Selfbilling: 'SELFBILLING' as PaymentAssignmentTargetType,
    SelfbillingReversed: 'SELFBILLING_REVERSED' as PaymentAssignmentTargetType,
    Commission: 'COMMISSION' as PaymentAssignmentTargetType,
    CommissionReversed: 'COMMISSION_REVERSED' as PaymentAssignmentTargetType
};

