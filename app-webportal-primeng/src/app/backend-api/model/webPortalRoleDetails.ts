/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserRight } from './userRight';


export interface WebPortalRoleDetails { 
    key: string;
    name: string;
    rights: Array<UserRight>;
    /**
     * The version of the role details
     */
    version: number;
}

