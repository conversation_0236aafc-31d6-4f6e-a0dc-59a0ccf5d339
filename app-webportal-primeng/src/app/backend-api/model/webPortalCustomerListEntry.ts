/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { CustomerType } from './customerType';


export interface WebPortalCustomerListEntry { 
    /**
     * The key of the customer
     */
    key: string;
    customerType: CustomerType;
    /**
     * The first name of the customer  (if private person)
     */
    firstName?: string;
    /**
     * The last name of the customer (if private person)
     */
    lastName?: string;
    /**
     * The name of the company (if company)
     */
    companyName?: string;
}
export namespace WebPortalCustomerListEntry {
}


