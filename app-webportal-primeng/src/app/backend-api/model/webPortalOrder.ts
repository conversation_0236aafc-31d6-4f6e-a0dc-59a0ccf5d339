/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OrderStatus } from './orderStatus';
import { Address } from './address';
import { OrderTotals } from './orderTotals';
import { FinancingType } from './financingType';
import { Property } from './property';


export interface WebPortalOrder { 
    key: string;
    version: number;
    businessSegmentKey?: string;
    /**
     * The date of the order
     */
    orderDate: string;
    /**
     * The date of the delivery
     */
    deliveryDate?: string;
    /**
     * The date the payment is due
     */
    paymentDueDate?: string;
    financingType: FinancingType;
    orderStatus: OrderStatus;
    shippingAddress?: Address;
    billingAddress?: Address;
    totals?: OrderTotals;
    properties?: Array<Property>;
    /**
     * The agreed deposit amount
     */
    agreedDepositAmount?: number;
}
export namespace WebPortalOrder {
}


