/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export type Currency = 'AED' | 'AFN' | 'ALL' | 'AMD' | 'ANG' | 'AOA' | 'ARS' | 'AUD' | 'AWG' | 'AZN' | 'BAM' | 'BBD' | 'BDT' | 'BGN' | 'BHD' | 'BIF' | 'BMD' | 'BND' | 'BOB' | 'BRL' | 'BSD' | 'BTN' | 'BWP' | 'BYN' | 'BZD' | 'CAD' | 'CDF' | 'CHF' | 'CLP' | 'CNY' | 'COP' | 'CRC' | 'CUC' | 'CUP' | 'CVE' | 'CZK' | 'DJF' | 'DKK' | 'DOP' | 'DZD' | 'EGP' | 'ERN' | 'ETB' | 'EUR' | 'FJD' | 'FKP' | 'GBP' | 'GEL' | 'GGP' | 'GHS' | 'GIP' | 'GMD' | 'GNF' | 'GTQ' | 'GYD' | 'HKD' | 'HNL' | 'HRK' | 'HTG' | 'HUF' | 'IDR' | 'ILS' | 'IMP' | 'INR' | 'IQD' | 'IRR' | 'ISK' | 'JEP' | 'JMD' | 'JOD' | 'JPY' | 'KES' | 'KGS' | 'KHR' | 'KMF' | 'KPW' | 'KRW' | 'KWD' | 'KYD' | 'KZT' | 'LAK' | 'LBP' | 'LKR' | 'LRD' | 'LSL' | 'LYD' | 'MAD' | 'MDL' | 'MGA' | 'MKD' | 'MMK' | 'MNT' | 'MOP' | 'MRU' | 'MUR' | 'MVR' | 'MWK' | 'MXN' | 'MYR' | 'MZN' | 'NAD' | 'NGN' | 'NIO' | 'NOK' | 'NPR' | 'NZD' | 'OMR' | 'PAB' | 'PEN' | 'PGK' | 'PHP' | 'PKR' | 'PLN' | 'PYG' | 'QAR' | 'RON' | 'RSD' | 'RUB' | 'RWF' | 'SAR' | 'SBD' | 'SCR' | 'SDG' | 'SEK' | 'SGD' | 'SHP' | 'SLL' | 'SOS' | 'SPL' | 'SRD' | 'STN' | 'SVC' | 'SYP' | 'SZL' | 'THB' | 'TJS' | 'TMT' | 'TND' | 'TOP' | 'TRY' | 'TTD' | 'TVD' | 'TWD' | 'TZS' | 'UAH' | 'UGX' | 'USD' | 'UYU' | 'UZS' | 'VEF' | 'VND' | 'VUV' | 'WST' | 'XAF' | 'XCD' | 'XDR' | 'XOF' | 'XPF' | 'YER' | 'ZAR' | 'ZMW' | 'ZWD';

export const Currency = {
    Aed: 'AED' as Currency,
    Afn: 'AFN' as Currency,
    All: 'ALL' as Currency,
    Amd: 'AMD' as Currency,
    Ang: 'ANG' as Currency,
    Aoa: 'AOA' as Currency,
    Ars: 'ARS' as Currency,
    Aud: 'AUD' as Currency,
    Awg: 'AWG' as Currency,
    Azn: 'AZN' as Currency,
    Bam: 'BAM' as Currency,
    Bbd: 'BBD' as Currency,
    Bdt: 'BDT' as Currency,
    Bgn: 'BGN' as Currency,
    Bhd: 'BHD' as Currency,
    Bif: 'BIF' as Currency,
    Bmd: 'BMD' as Currency,
    Bnd: 'BND' as Currency,
    Bob: 'BOB' as Currency,
    Brl: 'BRL' as Currency,
    Bsd: 'BSD' as Currency,
    Btn: 'BTN' as Currency,
    Bwp: 'BWP' as Currency,
    Byn: 'BYN' as Currency,
    Bzd: 'BZD' as Currency,
    Cad: 'CAD' as Currency,
    Cdf: 'CDF' as Currency,
    Chf: 'CHF' as Currency,
    Clp: 'CLP' as Currency,
    Cny: 'CNY' as Currency,
    Cop: 'COP' as Currency,
    Crc: 'CRC' as Currency,
    Cuc: 'CUC' as Currency,
    Cup: 'CUP' as Currency,
    Cve: 'CVE' as Currency,
    Czk: 'CZK' as Currency,
    Djf: 'DJF' as Currency,
    Dkk: 'DKK' as Currency,
    Dop: 'DOP' as Currency,
    Dzd: 'DZD' as Currency,
    Egp: 'EGP' as Currency,
    Ern: 'ERN' as Currency,
    Etb: 'ETB' as Currency,
    Eur: 'EUR' as Currency,
    Fjd: 'FJD' as Currency,
    Fkp: 'FKP' as Currency,
    Gbp: 'GBP' as Currency,
    Gel: 'GEL' as Currency,
    Ggp: 'GGP' as Currency,
    Ghs: 'GHS' as Currency,
    Gip: 'GIP' as Currency,
    Gmd: 'GMD' as Currency,
    Gnf: 'GNF' as Currency,
    Gtq: 'GTQ' as Currency,
    Gyd: 'GYD' as Currency,
    Hkd: 'HKD' as Currency,
    Hnl: 'HNL' as Currency,
    Hrk: 'HRK' as Currency,
    Htg: 'HTG' as Currency,
    Huf: 'HUF' as Currency,
    Idr: 'IDR' as Currency,
    Ils: 'ILS' as Currency,
    Imp: 'IMP' as Currency,
    Inr: 'INR' as Currency,
    Iqd: 'IQD' as Currency,
    Irr: 'IRR' as Currency,
    Isk: 'ISK' as Currency,
    Jep: 'JEP' as Currency,
    Jmd: 'JMD' as Currency,
    Jod: 'JOD' as Currency,
    Jpy: 'JPY' as Currency,
    Kes: 'KES' as Currency,
    Kgs: 'KGS' as Currency,
    Khr: 'KHR' as Currency,
    Kmf: 'KMF' as Currency,
    Kpw: 'KPW' as Currency,
    Krw: 'KRW' as Currency,
    Kwd: 'KWD' as Currency,
    Kyd: 'KYD' as Currency,
    Kzt: 'KZT' as Currency,
    Lak: 'LAK' as Currency,
    Lbp: 'LBP' as Currency,
    Lkr: 'LKR' as Currency,
    Lrd: 'LRD' as Currency,
    Lsl: 'LSL' as Currency,
    Lyd: 'LYD' as Currency,
    Mad: 'MAD' as Currency,
    Mdl: 'MDL' as Currency,
    Mga: 'MGA' as Currency,
    Mkd: 'MKD' as Currency,
    Mmk: 'MMK' as Currency,
    Mnt: 'MNT' as Currency,
    Mop: 'MOP' as Currency,
    Mru: 'MRU' as Currency,
    Mur: 'MUR' as Currency,
    Mvr: 'MVR' as Currency,
    Mwk: 'MWK' as Currency,
    Mxn: 'MXN' as Currency,
    Myr: 'MYR' as Currency,
    Mzn: 'MZN' as Currency,
    Nad: 'NAD' as Currency,
    Ngn: 'NGN' as Currency,
    Nio: 'NIO' as Currency,
    Nok: 'NOK' as Currency,
    Npr: 'NPR' as Currency,
    Nzd: 'NZD' as Currency,
    Omr: 'OMR' as Currency,
    Pab: 'PAB' as Currency,
    Pen: 'PEN' as Currency,
    Pgk: 'PGK' as Currency,
    Php: 'PHP' as Currency,
    Pkr: 'PKR' as Currency,
    Pln: 'PLN' as Currency,
    Pyg: 'PYG' as Currency,
    Qar: 'QAR' as Currency,
    Ron: 'RON' as Currency,
    Rsd: 'RSD' as Currency,
    Rub: 'RUB' as Currency,
    Rwf: 'RWF' as Currency,
    Sar: 'SAR' as Currency,
    Sbd: 'SBD' as Currency,
    Scr: 'SCR' as Currency,
    Sdg: 'SDG' as Currency,
    Sek: 'SEK' as Currency,
    Sgd: 'SGD' as Currency,
    Shp: 'SHP' as Currency,
    Sll: 'SLL' as Currency,
    Sos: 'SOS' as Currency,
    Spl: 'SPL' as Currency,
    Srd: 'SRD' as Currency,
    Stn: 'STN' as Currency,
    Svc: 'SVC' as Currency,
    Syp: 'SYP' as Currency,
    Szl: 'SZL' as Currency,
    Thb: 'THB' as Currency,
    Tjs: 'TJS' as Currency,
    Tmt: 'TMT' as Currency,
    Tnd: 'TND' as Currency,
    Top: 'TOP' as Currency,
    Try: 'TRY' as Currency,
    Ttd: 'TTD' as Currency,
    Tvd: 'TVD' as Currency,
    Twd: 'TWD' as Currency,
    Tzs: 'TZS' as Currency,
    Uah: 'UAH' as Currency,
    Ugx: 'UGX' as Currency,
    Usd: 'USD' as Currency,
    Uyu: 'UYU' as Currency,
    Uzs: 'UZS' as Currency,
    Vef: 'VEF' as Currency,
    Vnd: 'VND' as Currency,
    Vuv: 'VUV' as Currency,
    Wst: 'WST' as Currency,
    Xaf: 'XAF' as Currency,
    Xcd: 'XCD' as Currency,
    Xdr: 'XDR' as Currency,
    Xof: 'XOF' as Currency,
    Xpf: 'XPF' as Currency,
    Yer: 'YER' as Currency,
    Zar: 'ZAR' as Currency,
    Zmw: 'ZMW' as Currency,
    Zwd: 'ZWD' as Currency
};

