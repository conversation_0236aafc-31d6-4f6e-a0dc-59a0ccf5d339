/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ReportFormat } from './reportFormat';
import { ReportInterval } from './reportInterval';
import { ReportType } from './reportType';


export interface WebPortalReportConfigurationDetails { 
    key: string;
    reportType: ReportType;
    reportInterval: ReportInterval;
    reportFormat: ReportFormat;
    /**
     * The version of the report configuration
     */
    version: number;
}
export namespace WebPortalReportConfigurationDetails {
}


