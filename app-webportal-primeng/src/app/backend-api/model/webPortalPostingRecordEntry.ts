/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface WebPortalPostingRecordEntry { 
    /**
     * The key of the posting record entry
     */
    key: string;
    /**
     * The amount of the posting record entry
     */
    amount: number;
    /**
     * The debit account number of the posting record entry
     */
    debitAccountNumber: number;
    /**
     * The debit posting key of the posting record entry
     */
    debitPostingKey?: string;
    /**
     * The credit account number of the posting record entry
     */
    creditAccountNumber: number;
    /**
     * The credit posting key of the posting record entry
     */
    creditPostingKey?: string;
}

