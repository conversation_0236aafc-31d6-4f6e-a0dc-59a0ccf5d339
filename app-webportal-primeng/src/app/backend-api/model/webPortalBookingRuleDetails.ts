/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { BookingRuleType } from './bookingRuleType';
import { DebitCreditIndicator } from './debitCreditIndicator';


export interface WebPortalBookingRuleDetails { 
    name: string;
    bookingRuleType: BookingRuleType;
    debitCreditIndicator?: DebitCreditIndicator;
    itemGroups?: string;
    itemArticleNumbers?: string;
    itemCategories?: string;
    itemNames?: string;
    taxCodes?: string;
    bankAccountNumbers?: string;
    paymentCategories?: string;
    targetTypes?: string;
    debitAccountNumber: number;
    debitAccountIsParty: boolean;
    debitPostingKey?: string;
    creditAccountNumber: number;
    creditAccountIsParty: boolean;
    creditPostingKey?: string;
    key: string;
    version: number;
}
export namespace WebPortalBookingRuleDetails {
}


