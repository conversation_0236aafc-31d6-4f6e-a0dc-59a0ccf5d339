/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';
import { FinancingType } from './financingType';
import { Property } from './property';
import { WebPortalOrderItem } from './webPortalOrderItem';


export interface WebPortalOrderCapture { 
    key: string;
    businessSegmentKey: string;
    /**
     * The date of the order
     */
    orderDate: string;
    /**
     * The date of the delivery
     */
    deliveryDate: string;
    /**
     * The date the payment is due
     */
    paymentDueDate: string;
    financingType: FinancingType;
    customerKey: string;
    orderCurrency: Currency;
    properties?: Array<Property>;
    /**
     * The agreed deposit amount
     */
    agreedDepositAmount?: number;
    /**
     * The title of the order
     */
    title?: string;
    /**
     * The line items of the order
     */
    items: Array<WebPortalOrderItem>;
}
export namespace WebPortalOrderCapture {
}


