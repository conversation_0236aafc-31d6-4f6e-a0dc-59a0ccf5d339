/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WebPortalAuthorizationConfig } from './webPortalAuthorizationConfig';
import { WebPortalNotificationReceiverSubpathDetails } from './webPortalNotificationReceiverSubpathDetails';


export interface WebPortalNotificationReceiverDetails { 
    key: string;
    baseUrl: string;
    authorizationConfig: WebPortalAuthorizationConfig;
    /**
     * The subpaths that the receiver is subscribed to
     */
    subpaths: Array<WebPortalNotificationReceiverSubpathDetails>;
    /**
     * The version of the notification receiver
     */
    version: number;
}

