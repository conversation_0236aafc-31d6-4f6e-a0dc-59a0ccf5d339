/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WebPortalCustomerDetailsData } from './webPortalCustomerDetailsData';
import { WebPortalOrder } from './webPortalOrder';
import { WebPortalOrderBalanceCase } from './webPortalOrderBalanceCase';


export interface WebPortalOrderDetails { 
    customerData: WebPortalCustomerDetailsData;
    orderData: WebPortalOrder;
    balanceCaseData?: WebPortalOrderBalanceCase;
}

