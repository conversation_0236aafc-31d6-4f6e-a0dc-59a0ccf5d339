/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Role } from './role';


export interface WebPortalUserDetails { 
    key: string;
    email: string;
    firstName: string;
    lastName: string;
    roles: Array<Role>;
    /**
     * The version of the user details
     */
    version: number;
}

