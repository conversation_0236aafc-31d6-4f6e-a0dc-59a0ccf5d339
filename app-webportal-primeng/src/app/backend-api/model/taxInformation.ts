/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';


/**
 * Tax information
 */
export interface TaxInformation { 
    /**
     * The type of tax (e.g. VAT)
     */
    taxType: TaxInformation.TaxTypeEnum;
    /**
     * The tax code (e.g. D7, A9)
     */
    taxCode: string;
    taxRate: number;
    currency: Currency;
}
export namespace TaxInformation {
    export type TaxTypeEnum = 'VAT';
    export const TaxTypeEnum = {
        Vat: 'VAT' as TaxTypeEnum
    };
}


