/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface WebPortalBusinessSegmentDetails { 
    key: string;
    /**
     * The invoice number pattern
     */
    invoiceNumberPattern: string;
    /**
     * The VAT ID
     */
    vatId: string;
    /**
     * The address key of the originator
     */
    originatorAddressKey: string;
    /**
     * The logo for the invoice
     */
    logo?: Blob;
    /**
     * The version of the business segment details
     */
    version: number;
}

