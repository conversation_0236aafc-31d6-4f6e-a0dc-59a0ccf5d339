/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PaymentAssignmentTargetType } from './paymentAssignmentTargetType';
import { DebitCreditIndicator } from './debitCreditIndicator';
import { PaymentAssignmentStatus } from './paymentAssignmentStatus';
import { Currency } from './currency';
import { PaymentAssignmentEntityTargetType } from './paymentAssignmentEntityTargetType';
import { PaymentAssignmentOrigin } from './paymentAssignmentOrigin';
import { PaymentAssignmentType } from './paymentAssignmentType';


export interface WebPortalPaymentAssignmentDetails { 
    /**
     * The key of the payment assignment
     */
    key: string;
    /**
     * The id of the payment transaction
     */
    paymentTransactionKey: string;
    /**
     * The date of the booking
     */
    assignmentDateTime: string;
    /**
     * The id of the assigned entity
     */
    assignedEntityKey: string;
    targetType: PaymentAssignmentTargetType;
    entityTargetType?: PaymentAssignmentEntityTargetType;
    debitCreditIndicator: DebitCreditIndicator;
    type: PaymentAssignmentType;
    origin: PaymentAssignmentOrigin;
    /**
     * The assigned amount
     */
    amount: number;
    currency: Currency;
    /**
     * The key of the reversing payment assignment
     */
    reversingPaymentAssignmentKey?: string;
    status: PaymentAssignmentStatus;
}
export namespace WebPortalPaymentAssignmentDetails {
}


