/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The type of the notification
 */
export type NotificationType = 'DOCUMENT_CREATED' | 'POSTING_RECORD_CREATED' | 'PAYMENT_ASSIGNED' | 'CUSTOMER_CREATED' | 'SUPPLIER_CREATED' | 'PAYMENT_TRANSACTION_CREATED';

export const NotificationType = {
    DocumentCreated: 'DOCUMENT_CREATED' as NotificationType,
    PostingRecordCreated: 'POSTING_RECORD_CREATED' as NotificationType,
    PaymentAssigned: 'PAYMENT_ASSIGNED' as NotificationType,
    CustomerCreated: 'CUSTOMER_CREATED' as NotificationType,
    SupplierCreated: 'SUPPLIER_CREATED' as NotificationType,
    PaymentTransactionCreated: 'PAYMENT_TRANSACTION_CREATED' as NotificationType
};

