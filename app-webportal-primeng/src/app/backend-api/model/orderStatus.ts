/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The status of the order
 */
export type OrderStatus = 'INIT' | 'CANCELLED' | 'BILLED' | 'REVERSED' | 'PARTIALLY_BILLED' | 'WAITING_FOR_APPROVAL';

export const OrderStatus = {
    Init: 'INIT' as OrderStatus,
    Cancelled: 'CANCELLED' as OrderStatus,
    Billed: 'BILLED' as OrderStatus,
    Reversed: 'REVERSED' as OrderStatus,
    PartiallyBilled: 'PARTIALLY_BILLED' as OrderStatus,
    WaitingForApproval: 'WAITING_FOR_APPROVAL' as OrderStatus
};

