/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { BalanceCaseStatus } from './balanceCaseStatus';
import { Currency } from './currency';


export interface WebPortalOrderBalanceCase { 
    status: BalanceCaseStatus;
    currency: Currency;
    totalDebitAmount: number;
    totalCreditAmount: number;
    totalBalanceAmount: number;
}
export namespace WebPortalOrderBalanceCase {
}


