/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Address } from './address';
import { Language } from './language';
import { CustomerType } from './customerType';
import { Property } from './property';


export interface WebPortalCustomerDetailsData { 
    key: string;
    version: number;
    customerType: CustomerType;
    /**
     * The first name of the customer
     */
    firstName?: string;
    /**
     * The last name of the customer
     */
    lastName?: string;
    /**
     * The name of the company (if the customer is a company)
     */
    companyName?: string;
    /**
     * The VAT ID for the customer
     */
    vatId?: string;
    language: Language;
    properties?: Array<Property>;
    billingAddress: Address;
    shippingAddress?: Address;
}
export namespace WebPortalCustomerDetailsData {
}


