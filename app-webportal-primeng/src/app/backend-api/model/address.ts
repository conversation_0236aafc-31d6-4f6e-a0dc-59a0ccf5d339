/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface Address { 
    key: string;
    version: number;
    firstName?: string;
    lastName?: string;
    companyName?: string;
    /**
     * The street of the address
     */
    street: string;
    /**
     * The house number of the address
     */
    houseNumber?: string;
    /**
     * The city of the address
     */
    city: string;
    /**
     * The postal code of the address
     */
    postalCode: string;
    /**
     * The country of the address
     */
    country: string;
    /**
     * The mail of the address
     */
    mailAddress?: string;
    /**
     * The phone number of the address
     */
    phone?: string;
}

