/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';
import { FinancingType } from './financingType';


export interface WebPortalOrderUpdateDetails { 
    key: string;
    version: number;
    businessSegmentKey: string;
    customerKey: string;
    /**
     * The date of the order
     */
    orderDate: string;
    orderCurrency: Currency;
    /**
     * The date of the delivery
     */
    deliveryDate?: string;
    /**
     * The date the payment is due
     */
    paymentDueDate?: string;
    financingType: FinancingType;
    shippingAddressKey?: string;
}
export namespace WebPortalOrderUpdateDetails {
}


