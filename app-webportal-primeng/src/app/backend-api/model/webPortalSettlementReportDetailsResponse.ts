/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface WebPortalSettlementReportDetailsResponse { 
    key: string;
    creationDateTime: string;
    accountNumber: string;
    origin: WebPortalSettlementReportDetailsResponse.OriginEnum;
    format: WebPortalSettlementReportDetailsResponse.FormatEnum;
}
export namespace WebPortalSettlementReportDetailsResponse {
    export type OriginEnum = 'API' | 'WEB_PORTAL';
    export const OriginEnum = {
        Api: 'API' as OriginEnum,
        WebPortal: 'WEB_PORTAL' as OriginEnum
    };
    export type FormatEnum = 'MT940' | 'CAMT053' | 'JSON';
    export const FormatEnum = {
        Mt940: 'MT940' as FormatEnum,
        Camt053: 'CAMT053' as FormatEnum,
        <PERSON>son: 'JSON' as FormatEnum
    };
}


