/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AuthorizationType } from './authorizationType';


export interface WebPortalAuthorizationConfig { 
    authorizationType: AuthorizationType;
    username?: string;
    password?: string;
    fixedApiToken?: string;
    accessTokenUri?: string;
    clientId?: string;
    clientSecret?: string;
    roles?: Array<string>;
}
export namespace WebPortalAuthorizationConfig {
}


