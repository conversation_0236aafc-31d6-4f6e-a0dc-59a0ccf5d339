/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The indicator for a debit or credit transaction
 */
export type DebitCreditIndicator = 'DEBIT' | 'CREDIT';

export const DebitCreditIndicator = {
    Debit: 'DEBIT' as DebitCreditIndicator,
    Credit: 'CREDIT' as DebitCreditIndicator
};

