/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserRight } from './userRight';
import { WebPortalUserDetailsData } from './webPortalUserDetailsData';


export interface WebPortalCurrentUserResponse { 
    userData?: WebPortalUserDetailsData;
    tenants?: Array<string>;
    rights?: Array<UserRight>;
}

