/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export type AuthorizationType = 'NONE' | 'BASIC' | 'FIXED_API_TOKEN' | 'OAUTH2_WITH_CLIENT_ID_AND_SECRET';

export const AuthorizationType = {
    None: 'NONE' as AuthorizationType,
    Basic: 'BASIC' as AuthorizationType,
    FixedApiToken: 'FIXED_API_TOKEN' as AuthorizationType,
    Oauth2WithClientIdAndSecret: 'OAUTH2_WITH_CLIENT_ID_AND_SECRET' as AuthorizationType
};

