/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';


export interface SumAggregation { 
    key: string;
    currency: Currency;
    netSum: number;
    taxSum: number;
    grossSum: number;
}
export namespace SumAggregation {
}


