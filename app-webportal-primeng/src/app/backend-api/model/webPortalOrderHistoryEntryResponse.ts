/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ProcessingStatus } from './processingStatus';


export interface WebPortalOrderHistoryEntryResponse { 
    /**
     * The key of the history entry
     */
    key: string;
    /**
     * The number of the history entry
     */
    number: number;
    processingStatus: ProcessingStatus;
    /**
     * The creation date time of the history entry
     */
    createdAt: string;
    /**
     * The username of the history entry
     */
    username: string;
    /**
     * The additional information of the history entry
     */
    additionalInformation?: string;
}
export namespace WebPortalOrderHistoryEntryResponse {
}


