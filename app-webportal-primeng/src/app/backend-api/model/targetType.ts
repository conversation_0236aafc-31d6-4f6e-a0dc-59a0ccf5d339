/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The type of the target
 */
export type TargetType = 'ORDER' | 'CUSTOMER' | 'SUPPLIER' | 'POSTING_RECORD' | 'DOCUMENT' | 'PAYMENT_TRANSACTION' | 'PAYMENT_ASSIGNMENT';

export const TargetType = {
    Order: 'ORDER' as TargetType,
    Customer: 'CUSTOMER' as TargetType,
    Supplier: 'SUPPLIER' as TargetType,
    PostingRecord: 'POSTING_RECORD' as TargetType,
    Document: 'DOCUMENT' as TargetType,
    PaymentTransaction: 'PAYMENT_TRANSACTION' as TargetType,
    PaymentAssignment: 'PAYMENT_ASSIGNMENT' as TargetType
};

