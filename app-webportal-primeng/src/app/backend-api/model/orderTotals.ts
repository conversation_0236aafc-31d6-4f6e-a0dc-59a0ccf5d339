/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';


export interface OrderTotals { 
    /**
     * The total price amount of the order item
     */
    totalNetAmount?: number;
    totalNetCurrency?: Currency;
    /**
     * The total price amount of the order item
     */
    totalTaxAmount?: number;
    totalTaxCurrency?: Currency;
    /**
     * The total price amount of the order item
     */
    totalGrossAmount?: number;
    totalGrossCurrency?: Currency;
}
export namespace OrderTotals {
}


