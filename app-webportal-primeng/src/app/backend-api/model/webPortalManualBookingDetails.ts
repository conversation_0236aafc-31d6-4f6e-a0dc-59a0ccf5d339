/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';


export interface WebPortalManualBookingDetails { 
    key?: string;
    bookingText: string;
    amount: number;
    currency: Currency;
    debitAccount: number;
    debitPostingKey?: string;
    creditAccount: number;
    creditPostingKey?: string;
    /**
     * The version of the manual booking
     */
    version: number;
}
export namespace WebPortalManualBookingDetails {
}


