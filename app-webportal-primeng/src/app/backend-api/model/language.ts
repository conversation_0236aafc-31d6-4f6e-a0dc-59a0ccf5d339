/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export type Language = 'AF' | 'AM' | 'AR' | 'AZ' | 'BE' | 'BG' | 'BN' | 'BS' | 'CA' | 'CEB' | 'CO' | 'CS' | 'CY' | 'DA' | 'DE' | 'EL' | 'EN' | 'EO' | 'ES' | 'ET' | 'EU' | 'FA' | 'FI' | 'FR' | 'FY' | 'GA' | 'GD' | 'GL' | 'GU' | 'HA' | 'HAW' | 'HE' | 'HI' | 'HMN' | 'HR' | 'HT' | 'HU' | 'HY' | 'ID' | 'IG' | 'IS' | 'IT' | 'JA' | 'JV' | 'KA' | 'KK' | 'KM' | 'KN' | 'KO' | 'KU' | 'KY' | 'LA' | 'LB' | 'LO' | 'LT' | 'LV' | 'MG' | 'MI' | 'MK' | 'ML' | 'MN' | 'MR' | 'MS' | 'MT' | 'MY' | 'NE' | 'NL' | 'false' | 'NY' | 'PA' | 'PL' | 'PS' | 'PT' | 'RO' | 'RU' | 'SD' | 'SI' | 'SK' | 'SL' | 'SM' | 'SN' | 'SO' | 'SQ' | 'SR' | 'ST' | 'SU' | 'SV' | 'SW' | 'TA' | 'TE' | 'TG' | 'TH' | 'TL' | 'TR' | 'UK' | 'UR' | 'UZ' | 'VI' | 'XH' | 'YI' | 'YO' | 'ZH_CN' | 'ZH_TW' | 'ZU';

export const Language = {
    Af: 'AF' as Language,
    Am: 'AM' as Language,
    Ar: 'AR' as Language,
    Az: 'AZ' as Language,
    Be: 'BE' as Language,
    Bg: 'BG' as Language,
    Bn: 'BN' as Language,
    Bs: 'BS' as Language,
    Ca: 'CA' as Language,
    Ceb: 'CEB' as Language,
    Co: 'CO' as Language,
    Cs: 'CS' as Language,
    Cy: 'CY' as Language,
    Da: 'DA' as Language,
    De: 'DE' as Language,
    El: 'EL' as Language,
    En: 'EN' as Language,
    Eo: 'EO' as Language,
    Es: 'ES' as Language,
    Et: 'ET' as Language,
    Eu: 'EU' as Language,
    Fa: 'FA' as Language,
    Fi: 'FI' as Language,
    Fr: 'FR' as Language,
    Fy: 'FY' as Language,
    Ga: 'GA' as Language,
    Gd: 'GD' as Language,
    Gl: 'GL' as Language,
    Gu: 'GU' as Language,
    Ha: 'HA' as Language,
    Haw: 'HAW' as Language,
    He: 'HE' as Language,
    Hi: 'HI' as Language,
    Hmn: 'HMN' as Language,
    Hr: 'HR' as Language,
    Ht: 'HT' as Language,
    Hu: 'HU' as Language,
    Hy: 'HY' as Language,
    Id: 'ID' as Language,
    Ig: 'IG' as Language,
    Is: 'IS' as Language,
    It: 'IT' as Language,
    Ja: 'JA' as Language,
    Jv: 'JV' as Language,
    Ka: 'KA' as Language,
    Kk: 'KK' as Language,
    Km: 'KM' as Language,
    Kn: 'KN' as Language,
    Ko: 'KO' as Language,
    Ku: 'KU' as Language,
    Ky: 'KY' as Language,
    La: 'LA' as Language,
    Lb: 'LB' as Language,
    Lo: 'LO' as Language,
    Lt: 'LT' as Language,
    Lv: 'LV' as Language,
    Mg: 'MG' as Language,
    Mi: 'MI' as Language,
    Mk: 'MK' as Language,
    Ml: 'ML' as Language,
    Mn: 'MN' as Language,
    Mr: 'MR' as Language,
    Ms: 'MS' as Language,
    Mt: 'MT' as Language,
    My: 'MY' as Language,
    Ne: 'NE' as Language,
    Nl: 'NL' as Language,
    False: 'false' as Language,
    Ny: 'NY' as Language,
    Pa: 'PA' as Language,
    Pl: 'PL' as Language,
    Ps: 'PS' as Language,
    Pt: 'PT' as Language,
    Ro: 'RO' as Language,
    Ru: 'RU' as Language,
    Sd: 'SD' as Language,
    Si: 'SI' as Language,
    Sk: 'SK' as Language,
    Sl: 'SL' as Language,
    Sm: 'SM' as Language,
    Sn: 'SN' as Language,
    So: 'SO' as Language,
    Sq: 'SQ' as Language,
    Sr: 'SR' as Language,
    St: 'ST' as Language,
    Su: 'SU' as Language,
    Sv: 'SV' as Language,
    Sw: 'SW' as Language,
    Ta: 'TA' as Language,
    Te: 'TE' as Language,
    Tg: 'TG' as Language,
    Th: 'TH' as Language,
    Tl: 'TL' as Language,
    Tr: 'TR' as Language,
    Uk: 'UK' as Language,
    Ur: 'UR' as Language,
    Uz: 'UZ' as Language,
    Vi: 'VI' as Language,
    Xh: 'XH' as Language,
    Yi: 'YI' as Language,
    Yo: 'YO' as Language,
    ZhCn: 'ZH_CN' as Language,
    ZhTw: 'ZH_TW' as Language,
    Zu: 'ZU' as Language
};

