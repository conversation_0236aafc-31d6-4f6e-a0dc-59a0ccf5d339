/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WebPortalOrderTemplateItemRequest } from './webPortalOrderTemplateItemRequest';
import { Currency } from './currency';


export interface WebPortalOrderTemplateRequest { 
    key: string;
    title: string;
    description?: string;
    currency: Currency;
    /**
     * The line items of the order
     */
    items: Array<WebPortalOrderTemplateItemRequest>;
}
export namespace WebPortalOrderTemplateRequest {
}


