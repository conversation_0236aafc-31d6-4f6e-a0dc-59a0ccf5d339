/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OrderStatus } from './orderStatus';
import { Currency } from './currency';
import { FinancingType } from './financingType';


export interface WebPortalOrderListOrderData { 
    key: string;
    /**
     * The date of the order
     */
    orderDate: string;
    financingType: FinancingType;
    orderStatus: OrderStatus;
    /**
     * The total amount of the order
     */
    totalAmount: number;
    totalCurrency: Currency;
    /**
     * The business segment key
     */
    businessSegmentKey: string;
}
export namespace WebPortalOrderListOrderData {
}


