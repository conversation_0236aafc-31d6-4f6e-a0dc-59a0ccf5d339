/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SendingStatus } from './sendingStatus';
import { NotificationType } from './notificationType';
import { TargetType } from './targetType';


export interface WebPortalNotificationsListEntry { 
    /**
     * The key of the notification
     */
    key: string;
    /**
     * The date and time when the notification was created
     */
    createdAt: string;
    /**
     * The date and time of the notification
     */
    sentAt?: string;
    type: NotificationType;
    /**
     * The key of the target
     */
    targetKey: string;
    targetType: TargetType;
    sendingStatus: SendingStatus;
}
export namespace WebPortalNotificationsListEntry {
}


