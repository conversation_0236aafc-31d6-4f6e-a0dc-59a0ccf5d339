/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Language } from './language';
import { Property } from './property';


export interface WebPortalSupplierDetailsData { 
    key: string;
    version: number;
    /**
     * The name of the company
     */
    companyName: string;
    /**
     * The VAT ID for the supplier
     */
    vatId?: string;
    language: Language;
    properties?: Array<Property>;
}
export namespace WebPortalSupplierDetailsData {
}


