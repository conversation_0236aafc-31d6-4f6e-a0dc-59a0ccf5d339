/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SendingStatus } from './sendingStatus';
import { TargetType } from './targetType';
import { DocumentType } from './documentType';
import { SentArchiveType } from './sentArchiveType';


export interface WebPortalSentArchiveEntryDetails { 
    /**
     * The key of the sent archive entry
     */
    key: string;
    /**
     * The date and time of the sent archive entry
     */
    sentAt: string;
    type: SentArchiveType;
    /**
     * The key of the target
     */
    targetKey: string;
    targetType: TargetType;
    sendingStatus: SendingStatus;
    documentType?: DocumentType;
    /**
     * The email header of the sent archive entry
     */
    emailHeader?: string;
    /**
     * The email to of the sent archive entry
     */
    emailTo?: string;
    /**
     * The email body of the sent archive entry
     */
    emailBody?: string;
    /**
     * The email attachment filenames of the sent archive entry
     */
    emailAttachmentFilenames?: string;
}
export namespace WebPortalSentArchiveEntryDetails {
}


