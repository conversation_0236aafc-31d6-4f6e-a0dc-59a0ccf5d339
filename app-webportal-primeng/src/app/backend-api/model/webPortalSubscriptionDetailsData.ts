/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SubscriptionStatus } from './subscriptionStatus';
import { Currency } from './currency';
import { SubscriptionFrequency } from './subscriptionFrequency';


export interface WebPortalSubscriptionDetailsData { 
    /**
     * The unique key of the subscription
     */
    key: string;
    /**
     * The name of the subscription
     */
    name: string;
    /**
     * The description of the subscription
     */
    description?: string;
    /**
     * The key of the customer who owns this subscription
     */
    customerKey: string;
    status: SubscriptionStatus;
    frequency: SubscriptionFrequency;
    /**
     * The recurring amount for the subscription
     */
    amount: number;
    currency: Currency;
    /**
     * The start date of the subscription
     */
    startDate: string;
    /**
     * The end date of the subscription (optional)
     */
    endDate?: string;
    /**
     * The next billing date for the subscription
     */
    nextBillingDate: string;
    /**
     * The version of the subscription for optimistic locking
     */
    version?: number;
}
export namespace WebPortalSubscriptionDetailsData {
}


