/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The status of the subscription
 */
export type SubscriptionStatus = 'ACTIVE' | 'PAUSED' | 'CANCELLED' | 'EXPIRED';

export const SubscriptionStatus = {
    Active: 'ACTIVE' as SubscriptionStatus,
    Paused: 'PAUSED' as SubscriptionStatus,
    Cancelled: 'CANCELLED' as SubscriptionStatus,
    Expired: 'EXPIRED' as SubscriptionStatus
};

