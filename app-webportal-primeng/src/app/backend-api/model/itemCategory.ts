/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The category of the item
 */
export type ItemCategory = 'PHYSICAL_PRODUCT' | 'DIGITAL_PRODUCT' | 'SERVICE' | 'VOUCHER' | 'DISCOUNT' | 'VOUCHER_USE' | 'TRADE_IN' | 'GOVERNMENT_GRANT';

export const ItemCategory = {
    PhysicalProduct: 'PHYSICAL_PRODUCT' as ItemCategory,
    DigitalProduct: 'DIGITAL_PRODUCT' as ItemCategory,
    Service: 'SERVICE' as ItemCategory,
    Voucher: 'VOUCHER' as ItemCategory,
    Discount: 'DISCOUNT' as ItemCategory,
    VoucherUse: 'VOUCHER_USE' as ItemCategory,
    TradeIn: 'TRADE_IN' as ItemCategory,
    GovernmentGrant: 'GOVERNMENT_GRANT' as ItemCategory
};

