/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PaymentAccountType } from './paymentAccountType';


export interface WebPortalPaymentAccountDetails { 
    paymentAccountType: PaymentAccountType;
    name: string;
    description: string;
    accountId: string;
    accountHolder: string;
    bic?: string;
    key: string;
    defaultAccount: boolean;
    version: number;
}
export namespace WebPortalPaymentAccountDetails {
}


