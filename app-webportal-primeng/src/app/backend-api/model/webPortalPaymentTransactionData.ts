/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PaymentCategory } from './paymentCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';
import { Currency } from './currency';
import { PaymentTransactionAssignmentStatus } from './paymentTransactionAssignmentStatus';


export interface WebPortalPaymentTransactionData { 
    /**
     * The key of the payment transaction
     */
    key: string;
    debitCreditIndicator: DebitCreditIndicator;
    /**
     * The date of the booking
     */
    bookingDate: string;
    /**
     * The text of the payment transaction
     */
    text?: string;
    assignmentStatus: PaymentTransactionAssignmentStatus;
    /**
     * The total amount of the payment transaction
     */
    totalAmount: number;
    /**
     * The assigned amount of the payment transaction
     */
    assignedAmount: number;
    /**
     * The open amount of the payment transaction
     */
    openAmount: number;
    currency: Currency;
    paymentCategory: PaymentCategory;
}
export namespace WebPortalPaymentTransactionData {
}


