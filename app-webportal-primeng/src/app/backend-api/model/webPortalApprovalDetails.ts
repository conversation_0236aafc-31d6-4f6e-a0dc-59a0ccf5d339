/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ApprovalType } from './approvalType';
import { TargetType } from './targetType';
import { ApprovalStatus } from './approvalStatus';


export interface WebPortalApprovalDetails { 
    key: string;
    approvalType: ApprovalType;
    targetType: TargetType;
    targetKey: string;
    description: string;
    approvalStatus: ApprovalStatus;
    author: string;
    reviewer?: string;
    comment?: string;
    createdAt: string;
    version: number;
}
export namespace WebPortalApprovalDetails {
}


