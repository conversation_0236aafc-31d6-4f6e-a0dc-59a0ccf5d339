/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { Address } from '../model/address';
// @ts-ignore
import { WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest } from '../model/webPortalCustomerDetailsCustomerAddressStatusUpdateRequest';
// @ts-ignore
import { WebPortalCustomerDetailsData } from '../model/webPortalCustomerDetailsData';
// @ts-ignore
import { WebPortalCustomerDetailsResponse } from '../model/webPortalCustomerDetailsResponse';
// @ts-ignore
import { WebPortalCustomerShippingAddressesPagedResponse } from '../model/webPortalCustomerShippingAddressesPagedResponse';
// @ts-ignore
import { WebPortalCustomersPagedResponse } from '../model/webPortalCustomersPagedResponse';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';



@Injectable({
  providedIn: 'root'
})
export class WebPortalCustomerService {

    protected basePath = 'http://localhost:8080';
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();
    public encoder: HttpParameterCodec;

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string|string[], @Optional() configuration: Configuration) {
        if (configuration) {
            this.configuration = configuration;
        }
        if (typeof this.configuration.basePath !== 'string') {
            const firstBasePath = Array.isArray(basePath) ? basePath[0] : undefined;
            if (firstBasePath != undefined) {
                basePath = firstBasePath;
            }

            if (typeof basePath !== 'string') {
                basePath = this.basePath;
            }
            this.configuration.basePath = basePath;
        }
        this.encoder = this.configuration.encoder || new CustomHttpParameterCodec();
    }


    // @ts-ignore
    private addToHttpParams(httpParams: HttpParams, value: any, key?: string): HttpParams {
        if (typeof value === "object" && value instanceof Date === false) {
            httpParams = this.addToHttpParamsRecursive(httpParams, value);
        } else {
            httpParams = this.addToHttpParamsRecursive(httpParams, value, key);
        }
        return httpParams;
    }

    private addToHttpParamsRecursive(httpParams: HttpParams, value?: any, key?: string): HttpParams {
        if (value == null) {
            return httpParams;
        }

        if (typeof value === "object") {
            if (Array.isArray(value)) {
                (value as any[]).forEach( elem => httpParams = this.addToHttpParamsRecursive(httpParams, elem, key));
            } else if (value instanceof Date) {
                if (key != null) {
                    httpParams = httpParams.append(key, (value as Date).toISOString().substring(0, 10));
                } else {
                   throw Error("key may not be null if value is Date");
                }
            } else {
                Object.keys(value).forEach( k => httpParams = this.addToHttpParamsRecursive(
                    httpParams, value[k], key != null ? `${key}.${k}` : k));
            }
        } else if (key != null) {
            httpParams = httpParams.append(key, value);
        } else {
            throw Error("key may not be null if value is not object or array");
        }
        return httpParams;
    }

    /**
     * Creates a new shipping address for a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param address 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public addCustomerShippingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<Address>;
    public addCustomerShippingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<Address>>;
    public addCustomerShippingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<Address>>;
    public addCustomerShippingAddress(xTenantKey: string, customerKey: string, address: Address, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling addCustomerShippingAddress.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling addCustomerShippingAddress.');
        }
        if (address === null || address === undefined) {
            throw new Error('Required parameter address was null or undefined when calling addCustomerShippingAddress.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/shippingaddresses`;
        return this.httpClient.request<Address>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: address,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a new customer
     * @param xTenantKey The key of the tenant
     * @param webPortalCustomerDetailsData 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createCustomer(xTenantKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomerDetailsResponse>;
    public createCustomer(xTenantKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomerDetailsResponse>>;
    public createCustomer(xTenantKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomerDetailsResponse>>;
    public createCustomer(xTenantKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling createCustomer.');
        }
        if (webPortalCustomerDetailsData === null || webPortalCustomerDetailsData === undefined) {
            throw new Error('Required parameter webPortalCustomerDetailsData was null or undefined when calling createCustomer.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers`;
        return this.httpClient.request<WebPortalCustomerDetailsResponse>('post', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: webPortalCustomerDetailsData,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadCustomer(xTenantKey: string, customerKey: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomerDetailsResponse>;
    public loadCustomer(xTenantKey: string, customerKey: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomerDetailsResponse>>;
    public loadCustomer(xTenantKey: string, customerKey: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomerDetailsResponse>>;
    public loadCustomer(xTenantKey: string, customerKey: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadCustomer.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling loadCustomer.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalCustomerDetailsResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load all shipping addresses of a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the addresses by. Supported values are \&#39;createdAt\&#39;, \&#39;addressLine1\&#39;, \&#39;city\&#39;, \&#39;state\&#39;, \&#39;country\&#39;
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the addresses. This can be any attribute of the address.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadCustomerShippingAddresses(xTenantKey: string, customerKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomerShippingAddressesPagedResponse>;
    public loadCustomerShippingAddresses(xTenantKey: string, customerKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomerShippingAddressesPagedResponse>>;
    public loadCustomerShippingAddresses(xTenantKey: string, customerKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomerShippingAddressesPagedResponse>>;
    public loadCustomerShippingAddresses(xTenantKey: string, customerKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadCustomerShippingAddresses.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling loadCustomerShippingAddresses.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/shippingaddresses`;
        return this.httpClient.request<WebPortalCustomerShippingAddressesPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Load all customers
     * @param xTenantKey The key of the tenant
     * @param page The index of the page to retrieve
     * @param size The number of items per page
     * @param sort The field to sort the orders by. Supported values are \&#39;createdAt\&#39;, \&#39;firstName\&#39;, \&#39;lastName\&#39;, \&#39;companyName\&#39;
     * @param direction The direction to sort by. Supported values are \&#39;asc\&#39;, \&#39;desc\&#39;
     * @param filter The filter to apply on the customers. This can be any attribute of the customer.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public loadCustomers(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomersPagedResponse>;
    public loadCustomers(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomersPagedResponse>>;
    public loadCustomers(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomersPagedResponse>>;
    public loadCustomers(xTenantKey: string, page?: number, size?: number, sort?: string, direction?: string, filter?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling loadCustomers.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (page !== undefined && page !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>page, 'page');
        }
        if (size !== undefined && size !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>size, 'size');
        }
        if (sort !== undefined && sort !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>sort, 'sort');
        }
        if (direction !== undefined && direction !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>direction, 'direction');
        }
        if (filter !== undefined && filter !== null) {
          localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
            <any>filter, 'filter');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers`;
        return this.httpClient.request<WebPortalCustomersPagedResponse>('get', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param webPortalCustomerDetailsData 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateCustomer(xTenantKey: string, customerKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomerDetailsResponse>;
    public updateCustomer(xTenantKey: string, customerKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomerDetailsResponse>>;
    public updateCustomer(xTenantKey: string, customerKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomerDetailsResponse>>;
    public updateCustomer(xTenantKey: string, customerKey: string, webPortalCustomerDetailsData: WebPortalCustomerDetailsData, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateCustomer.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling updateCustomer.');
        }
        if (webPortalCustomerDetailsData === null || webPortalCustomerDetailsData === undefined) {
            throw new Error('Required parameter webPortalCustomerDetailsData was null or undefined when calling updateCustomer.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<WebPortalCustomerDetailsResponse>('patch', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: webPortalCustomerDetailsData,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update the status of a specific shipping address
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param addressKey 
     * @param webPortalCustomerDetailsCustomerAddressStatusUpdateRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateCustomerAddressStatus(xTenantKey: string, customerKey: string, addressKey: string, webPortalCustomerDetailsCustomerAddressStatusUpdateRequest: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<WebPortalCustomerDetailsResponse>;
    public updateCustomerAddressStatus(xTenantKey: string, customerKey: string, addressKey: string, webPortalCustomerDetailsCustomerAddressStatusUpdateRequest: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<WebPortalCustomerDetailsResponse>>;
    public updateCustomerAddressStatus(xTenantKey: string, customerKey: string, addressKey: string, webPortalCustomerDetailsCustomerAddressStatusUpdateRequest: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<WebPortalCustomerDetailsResponse>>;
    public updateCustomerAddressStatus(xTenantKey: string, customerKey: string, addressKey: string, webPortalCustomerDetailsCustomerAddressStatusUpdateRequest: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateCustomerAddressStatus.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling updateCustomerAddressStatus.');
        }
        if (addressKey === null || addressKey === undefined) {
            throw new Error('Required parameter addressKey was null or undefined when calling updateCustomerAddressStatus.');
        }
        if (webPortalCustomerDetailsCustomerAddressStatusUpdateRequest === null || webPortalCustomerDetailsCustomerAddressStatusUpdateRequest === undefined) {
            throw new Error('Required parameter webPortalCustomerDetailsCustomerAddressStatusUpdateRequest was null or undefined when calling updateCustomerAddressStatus.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/shippingaddresses/${this.configuration.encodeParam({name: "addressKey", value: addressKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/status`;
        return this.httpClient.request<WebPortalCustomerDetailsResponse>('put', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: webPortalCustomerDetailsCustomerAddressStatusUpdateRequest,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update the billing address of a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param address 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateCustomerBillingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<Address>;
    public updateCustomerBillingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<Address>>;
    public updateCustomerBillingAddress(xTenantKey: string, customerKey: string, address: Address, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<Address>>;
    public updateCustomerBillingAddress(xTenantKey: string, customerKey: string, address: Address, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateCustomerBillingAddress.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling updateCustomerBillingAddress.');
        }
        if (address === null || address === undefined) {
            throw new Error('Required parameter address was null or undefined when calling updateCustomerBillingAddress.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/billingaddress`;
        return this.httpClient.request<Address>('put', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: address,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update the shipping address of a specific customer
     * @param xTenantKey The key of the tenant
     * @param customerKey 
     * @param addressKey 
     * @param address 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateCustomerShippingAddress(xTenantKey: string, customerKey: string, addressKey: string, address: Address, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<Address>;
    public updateCustomerShippingAddress(xTenantKey: string, customerKey: string, addressKey: string, address: Address, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<Address>>;
    public updateCustomerShippingAddress(xTenantKey: string, customerKey: string, addressKey: string, address: Address, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<Address>>;
    public updateCustomerShippingAddress(xTenantKey: string, customerKey: string, addressKey: string, address: Address, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (xTenantKey === null || xTenantKey === undefined) {
            throw new Error('Required parameter xTenantKey was null or undefined when calling updateCustomerShippingAddress.');
        }
        if (customerKey === null || customerKey === undefined) {
            throw new Error('Required parameter customerKey was null or undefined when calling updateCustomerShippingAddress.');
        }
        if (addressKey === null || addressKey === undefined) {
            throw new Error('Required parameter addressKey was null or undefined when calling updateCustomerShippingAddress.');
        }
        if (address === null || address === undefined) {
            throw new Error('Required parameter address was null or undefined when calling updateCustomerShippingAddress.');
        }

        let localVarHeaders = this.defaultHeaders;
        if (xTenantKey !== undefined && xTenantKey !== null) {
            localVarHeaders = localVarHeaders.set('x-tenant-key', String(xTenantKey));
        }

        let localVarHttpHeaderAcceptSelected: string | undefined = options && options.httpHeaderAccept;
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/json'
            ];
            localVarHttpHeaderAcceptSelected = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        let localVarHttpContext: HttpContext | undefined = options && options.context;
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext();
        }

        let localVarTransferCache: boolean | undefined = options && options.transferCache;
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true;
        }


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/webportal/v1/customers/${this.configuration.encodeParam({name: "customerKey", value: customerKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}/shippingaddresses/${this.configuration.encodeParam({name: "addressKey", value: addressKey, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        return this.httpClient.request<Address>('put', `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: address,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
