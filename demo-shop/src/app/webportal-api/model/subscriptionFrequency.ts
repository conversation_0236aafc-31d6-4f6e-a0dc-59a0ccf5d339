/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The billing frequency of the subscription
 */
export type SubscriptionFrequency = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY';

export const SubscriptionFrequency = {
    Daily: 'DAILY' as SubscriptionFrequency,
    Weekly: 'WEEKLY' as SubscriptionFrequency,
    Monthly: 'MONTHLY' as SubscriptionFrequency,
    Quarterly: 'QUARTERLY' as SubscriptionFrequency,
    Yearly: 'YEARLY' as SubscriptionFrequency
};

