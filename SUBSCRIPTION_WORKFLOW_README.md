# Subscription Workflow Implementation

This document describes the implementation of subscription processing via workflows, similar to how Orders are currently processed.

## Overview

The system now supports processing Subscriptions via Workflows, enabling automated subscription lifecycle management including creation, activation, billing, pausing, resuming, and cancellation.

## Architecture

### Workflow Parameter Classes
- `StartSubscriptionWorkflowParameters` - Parameters for starting subscription workflows
- `SubscriptionWorkflowParameters` - Runtime parameters for subscription workflow execution
- Extended `ParameterKeys` with subscription-specific parameter keys

### Processing Status Extensions
Added new processing status values for subscriptions:
- `SUBSCRIPTION_CREATED` - Subscription has been created
- `SUBSCRIPTION_ACTIVATED` - Subscription has been activated
- `SUBSCRIPTION_BILLING_PROCESSED` - Billing cycle has been processed
- `SUBSCRIPTION_INVOICE_CREATED` - Invoice has been generated
- `SUBSCRIPTION_INVOICE_SENT` - Invoice has been sent to customer
- `SUBSCRIPTION_PAUSED` - Subscription has been paused
- `SUBSCRIPTION_RESUMED` - Subscription has been resumed
- `SUBSCRIPTION_CANCELLED` - Subscription has been cancelled
- `SUBSCRIPTION_EXPIRED` - Subscription has expired
- `SUBSCRIPTION_UPDATED` - Subscription has been updated

### Entity Enhancements
- Added `processingStatus` field to `Subscription` entity
- Enhanced `SubscriptionService` with `updateSubscriptionProcessingStatus()` method

### Workflow Step Services
Created subscription-specific workflow step services:
- `CreateSubscriptionService` - Creates initial subscription
- `ActivateSubscriptionService` - Activates subscription using service methods
- `ProcessSubscriptionBillingService` - Processes billing cycles using service methods
- `CreateSubscriptionInvoiceService` - Creates invoices
- `SendSubscriptionInvoiceService` - Sends invoices via email
- `PauseSubscriptionService` - Pauses subscription using service methods
- `ResumeSubscriptionService` - Resumes subscription using service methods
- `CancelSubscriptionService` - Cancels subscription using service methods

### Enhanced SubscriptionService
Extended `SubscriptionService` with dedicated methods for workflow operations:
- `updateSubscriptionStatus()` - Updates subscription status (ACTIVE, PAUSED, CANCELLED)
- `updateSubscriptionNextBillingDate()` - Updates next billing date for billing cycles
- `updateSubscriptionProcessingStatus()` - Updates processing status for workflow tracking

All workflow services now use proper service methods instead of direct database access.

### Subscription History Tracking
Implemented comprehensive subscription history tracking similar to order history:
- `SubscriptionHistoryEntry` entity - Tracks all subscription status changes
- `SubscriptionHistoryService` - Manages history entry creation
- `SubscriptionHistoryEntryLoadService` - Loads and queries history entries
- `SubscriptionHistoryEntryLoadDomainApiService` - Domain API for history access
- `SubscriptionHistoryEntryController` - REST API for history retrieval
- Automatic history creation on every processing status update
- Sequential numbering of history entries per subscription
- Full audit trail with timestamps and user tracking

### Workflow Definitions
Created BPMN workflow definitions:
- `StandardSubscription.bpmn` - Standard subscription workflow
- `VelocityWheelsSubscription.bpmn` - Velocity Wheels specific subscription workflow

### Configuration Updates
- Added `subscriptionWorkflow` field to `TenantConfig`
- Updated `WorkflowDeploymentService` to deploy subscription workflows
- Updated Velocity Wheels tenant configuration to use subscription workflow

## Velocity Wheels Demo

### Configuration
Velocity Wheels is configured to use the `velocityWheelsSubscription` workflow for car leasing subscriptions.

### Demo Features
- Monthly car lease subscriptions (BMW X5 example)
- Automated billing cycle processing
- Invoice generation and email delivery
- Subscription lifecycle management (pause/resume/cancel)

### Demo Endpoints
- `GET /api/demo/subscription/velocity-wheels` - Runs the Velocity Wheels subscription demo

### History API Endpoints
- `GET /api/tenants/{tenantKey}/subscriptions/{subscriptionKey}/history` - Retrieves subscription history
  - Supports pagination and filtering
  - Returns chronological list of all subscription status changes
  - Includes processing status, timestamps, user information, and additional details

## Usage

### Starting a Subscription Workflow
```kotlin
workflowApiService.startSubscriptionWorkflow(
    tenantKey = "VelocityWheels",
    userKey = "system",
    subscriptionDomainDto = subscriptionDto
)
```

### Subscription Lifecycle Events
The workflow supports the following events:
- `processSubscriptionBilling` - Triggers billing cycle
- `pauseSubscription` - Pauses the subscription
- `resumeSubscription` - Resumes the subscription
- `cancelSubscription` - Cancels the subscription

## Workflow Flow

### Standard Subscription Workflow
1. **Create Subscription** - Initial subscription creation
2. **Activate Subscription** - Activate the subscription
3. **Process Billing** - Handle billing cycle
4. **Create Invoice** - Generate invoice
5. **Send Invoice** - Send invoice to customer
6. **Wait for Events** - Listen for lifecycle events (billing, pause, resume, cancel)

### Velocity Wheels Subscription Workflow
Enhanced version with additional car leasing specific features:
- Specialized billing subprocess
- Enhanced event handling
- Car leasing specific business logic

## Testing

### Integration Test
Run the subscription workflow integration test:
```bash
./gradlew test --tests SubscriptionWorkflowIntegrationTest
```

### Demo Test
Test the Velocity Wheels demo:
```bash
curl http://localhost:8080/api/demo/subscription/velocity-wheels
```

## Database Changes

### Required Schema Updates
The `subscriptions` table needs to include the `processing_status` column:
```sql
ALTER TABLE subscriptions ADD COLUMN processing_status VARCHAR(255) DEFAULT 'INIT';
```

The `tenant_configs` table needs to include the `subscription_workflow` column:
```sql
ALTER TABLE tenant_configs ADD COLUMN subscription_workflow VARCHAR(255) DEFAULT 'standardSubscription';
```

The `subscription_history_entries` table for tracking subscription changes:
```sql
CREATE TABLE subscription_history_entries (
    id UUID PRIMARY KEY,
    number BIGINT NOT NULL,
    subscription_id UUID NOT NULL REFERENCES subscriptions(id),
    processing_status VARCHAR(100) NOT NULL,
    additional_information VARCHAR(1000),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    key VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    created_by UUID NOT NULL,
    UNIQUE(tenant_id, key)
);
```

## Future Enhancements

### Planned Features
1. ✅ **Subscription History Tracking** - Complete audit trail implemented
2. **Advanced Billing Rules** - Proration, discounts, etc.
3. **Payment Integration** - Automated payment processing
4. **Notification System** - Customer notifications for lifecycle events
5. **Subscription Analytics** - Reporting and metrics

### Integration Points
- **Order Generation** - Create orders from subscription billing
- **Payment Processing** - Integrate with payment systems
- **Customer Communication** - Email/SMS notifications
- **Reporting** - Subscription metrics and analytics

## Troubleshooting

### Common Issues
1. **Workflow Not Found** - Ensure BPMN files are deployed correctly
2. **Processing Status Not Updated** - Check workflow step execution
3. **Demo Customer Not Found** - Ensure demo data is loaded

### Logging
Enable debug logging for subscription workflows:
```yaml
logging:
  level:
    com.klosesoft.billingsolution.workflow.steps.subscription: DEBUG
```

## Support

For questions or issues with the subscription workflow implementation, please refer to the development team or create an issue in the project repository.
