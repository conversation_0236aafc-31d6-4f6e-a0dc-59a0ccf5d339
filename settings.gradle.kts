rootProject.name = "billing-solution"

pluginManagement {
    repositories {
        mavenLocal()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        mavenCentral()

        maven {
            url = uri("https://artifacts.alfresco.com/nexus/content/repositories/public/")
        }
    }
}

include("app-frontend")
include("acceptance-tests")
include("common-network")
include("common-utils")
include("workflow")
include("domain-model-api")
include("databasefiller")
include("persistence")
include("workflow-api")
include("common-test")
include("domain-logic")
include("domain-logic-api")
include("api-external-client")
include("api-external-server")
include("api-outgoing-notification-client")
include("api-outgoing-notification-server")
include("api-webportal-server")
include("api-volt-client")
include("acceptance-tests-gui")
include("app-webportal")
include("app-webportal-primeng")
include("demo-shop")
include("demo-subscription-workflow")
include("tool-goa-extractor")
