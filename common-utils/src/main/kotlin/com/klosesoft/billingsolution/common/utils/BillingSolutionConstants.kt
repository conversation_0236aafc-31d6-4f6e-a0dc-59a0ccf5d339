package com.klosesoft.billingsolution.common.utils

import java.util.UUID

object BillingSolutionConstants {
    //    ############ Start User Ids ############
    val SYSTEM_USER_ID: UUID = UUID.fromString("00000000-0000-0000-0000-000000000000")
    const val SYSTEM_USER_KEY: String = "system"
    //    ############ End User Ids ############

    //    ############ Start Tenant Ids ############
    const val TENANT_KEY_BIKESALE_UK: String = "BikeSaleUK"
    const val TENANT_KEY_SUNPOWER: String = "Sunpower"
    const val TENANT_KEY_MVZ_GESUND: String = "MVZGesund"
    const val TENANT_KEY_VELOCITY_WHEELS: String = "VelocityWheels"
    //    ############ End Tenant Ids ############

    //    ############ Start Workflow Identifiers ############

    const val STANDARD_ORDER_WORKFLOW = "standardOrder"
    const val BIKESALE_UK_WORKFLOW = "bikeSaleUkOrder"
    const val MVZ_GESUND_ORDER_WORKFLOW = "mvzGesundOrder"

    const val STANDARD_SUBSCRIPTION_WORKFLOW = "standardSubscription"
    const val VELOCITY_WHEELS_SUBSCRIPTION_WORKFLOW = "velocityWheelsSubscription"

    //    ############ End Workflow Identifiers ############

    const val EXTERNAL_API_ROLE: String = "https://auth.klosesoft.com/external-api"
    const val WEBPORTAL_API_ROLE: String = "https://auth.klosesoft.com/webportal-api"
    const val WEBPORTAL_ADMIN_API_ROLE: String = "aws.cognito.signin.user.admin"

    const val CORRELATION_ID_KEY: String = "correlation-id"
    const val REQUEST_METHOD_KEY: String = "request-method"
    const val RESPONSE_TIME_KEY: String = "response-time"
    const val STATUS_CODE_KEY: String = "status-code"

    const val HEADER_AUTHORIZATION: String = "Authorization"
    const val BEARER: String = "Bearer"

    const val ENV_PROD: String = "prod"

    const val DEFAULT_INTERNAL_PAGE_SIZE: Int = 100
}
