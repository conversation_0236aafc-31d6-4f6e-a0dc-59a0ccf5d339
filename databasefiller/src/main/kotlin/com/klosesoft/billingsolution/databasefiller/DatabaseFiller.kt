package com.klosesoft.billingsolution.databasefiller

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_BIKESALE_UK
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_MVZ_GESUND
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS
import com.klosesoft.billingsolution.common.utils.exception.EntityNotFoundException
import com.klosesoft.billingsolution.databasefiller.tenant.BikeSaleUkInitializer
import com.klosesoft.billingsolution.databasefiller.tenant.MvzGesundInitializer
import com.klosesoft.billingsolution.databasefiller.tenant.SunpowerInitializer
import com.klosesoft.billingsolution.databasefiller.tenant.VelocityWheelsInitializer
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.persistence.model.entity.User
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.springframework.boot.ApplicationArguments
import org.springframework.boot.ApplicationRunner
import org.springframework.stereotype.Component

@Component
class DatabaseFiller(
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val tenantLoadService: TenantLoadService,

    //   Tenant specific business data
    private val bikeSaleUkInitializer: BikeSaleUkInitializer,
    private val sunpowerInitializer: SunpowerInitializer,
    private val mvzGesundInitializer: MvzGesundInitializer,
    private val velocityWheelsInitializer: VelocityWheelsInitializer,
) : ApplicationRunner {
    private val logger = KotlinLogging.logger { }

    @OptIn(DelicateCoroutinesApi::class)
    override fun run(
        args: ApplicationArguments?,
    ) {
        logger.info { "Filling database started..." }

        GlobalScope.launch {
            initApplicationData()
            logger.info { "Finished filling database" }
        }
    }

    private suspend fun initApplicationData() {
        if (isSystemUserPresent()) return

        createAdminUsers()
        createWebPortalUsers()

        bikeSaleUkInitializer.setupTenant()
        sunpowerInitializer.setupTenant()
        mvzGesundInitializer.setupTenant()
        velocityWheelsInitializer.setupTenant()

        updateDefaultTenantUsers()
    }

    private suspend fun updateDefaultTenantUsers() {
        userService.updateDefaultTenantIdForUser(
            userLoadService.findByKey("<EMAIL>").copy(
                lastModifiedBy = SYSTEM_USER_ID,
                defaultTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_BIKESALE_UK).id,
            ),
        )
        userService.updateDefaultTenantIdForUser(
            userLoadService.findByKey("<EMAIL>").copy(
                lastModifiedBy = SYSTEM_USER_ID,
                defaultTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_MVZ_GESUND).id,
            ),
        )
        userService.updateDefaultTenantIdForUser(
            userLoadService.findByKey("<EMAIL>").copy(
                lastModifiedBy = SYSTEM_USER_ID,
                defaultTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_MVZ_GESUND).id,
            ),
        )
        userService.updateDefaultTenantIdForUser(
            userLoadService.findByKey("<EMAIL>").copy(
                lastModifiedBy = SYSTEM_USER_ID,
                defaultTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id,
            ),
        )
    }

    private suspend fun isSystemUserPresent(): Boolean {
        try {
            userLoadService.findByKey(SYSTEM_USER_KEY)
            return true
        } catch (e: EntityNotFoundException) {
            return false
        }
    }

    private suspend fun createAdminUsers() {
        userService.createUser(createSystemUser(), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createWebPortalUsers() {
        userService.createUser(createPklWebPortalUser(), emptyList(), SYSTEM_USER_ID)
        userService.createUser(createFlfWebPortalUser(), emptyList(), SYSTEM_USER_ID)
        userService.createUser(createJzaWebPortalUser(), emptyList(), SYSTEM_USER_ID)
        userService.createUser(createReadOnlyWebPortalUser(), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createSystemUser(): User {
        val user =
            User(
                firstName = SYSTEM_USER_KEY,
                lastName = SYSTEM_USER_KEY,
                key = SYSTEM_USER_KEY,
                email = "<EMAIL>",
                createdBy = SYSTEM_USER_ID,
                lastModifiedBy = SYSTEM_USER_ID,
            )

        return user
    }

    private suspend fun createPklWebPortalUser() = User(
        firstName = "Paul",
        lastName = "Klose",
        email = "<EMAIL>",
        key = "<EMAIL>",
        createdBy = SYSTEM_USER_ID,
        lastModifiedBy = SYSTEM_USER_ID,
    )

    private suspend fun createFlfWebPortalUser(): User {
        val user =
            User(
                firstName = "Florian",
                lastName = "Fittkau",
                email = "<EMAIL>",
                key = "<EMAIL>",
                createdBy = SYSTEM_USER_ID,
                lastModifiedBy = SYSTEM_USER_ID,
            )

        return user
    }

    private suspend fun createJzaWebPortalUser(): User {
        val user =
            User(
                firstName = "Julian",
                lastName = "Zaruba",
                email = "<EMAIL>",
                key = "<EMAIL>",
                createdBy = SYSTEM_USER_ID,
                lastModifiedBy = SYSTEM_USER_ID,
            )

        return user
    }

    private suspend fun createReadOnlyWebPortalUser(): User {
        val user =
            User(
                firstName = "Read",
                lastName = "Only",
                email = "<EMAIL>",
                key = "<EMAIL>",
                createdBy = SYSTEM_USER_ID,
                lastModifiedBy = SYSTEM_USER_ID,
            )

        return user
    }
}
