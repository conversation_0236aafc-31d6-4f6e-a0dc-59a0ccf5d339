package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantUpdatableBaseEntity
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Table
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Table(name = "subscriptions")
data class Subscription(
    var name: String,
    var description: String? = null,
    val customerId: UUID,
    var status: SubscriptionStatus,
    val processingStatus: ProcessingStatus = ProcessingStatus.INIT,
    var frequency: SubscriptionFrequency,
    var amount: BigDecimal,
    val currency: Currency,
    val startDate: LocalDate,
    var endDate: LocalDate? = null,
    var nextBillingDate: LocalDate,

    override val tenantId: UUID,
    override val key: String,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
    override val lastModifiedBy: UUID,
    override val lastModifiedAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    @Version
    override val version: Long = 0,
) : TenantUpdatableBaseEntity
