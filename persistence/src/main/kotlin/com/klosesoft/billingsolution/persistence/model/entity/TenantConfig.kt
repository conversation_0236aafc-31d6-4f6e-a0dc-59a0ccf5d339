package com.klosesoft.billingsolution.persistence.model.entity

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.StorageType
import com.klosesoft.billingsolution.persistence.model.entity.base.TenantUpdatableBaseEntity
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Table
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.TimeZone
import java.util.UUID

@Table(name = "tenant_configs")
data class TenantConfig(
    /**
     * Used for external API authentication
     */
    val appClientId: String,
    val storageType: StorageType = StorageType.S3,
    val orderWorkflow: String = BillingSolutionConstants.STANDARD_ORDER_WORKFLOW,
    val subscriptionWorkflow: String = BillingSolutionConstants.STANDARD_SUBSCRIPTION_WORKFLOW,
    val ledgerCurrency: Currency,
    val locale: String,
    val theme: String,
    val timeZone: String,
    val defaultLanguage: Language,
    val defaultTaxRate: BigDecimal,
    val features: List<Feature>,

    override val tenantId: UUID,
    override val key: String,
    override val id: UUID = UUID.randomUUID(),
    override val createdAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    override val createdBy: UUID,
    override val lastModifiedBy: UUID,
    override val lastModifiedAt: LocalDateTime = TimeProvider.nowDateTimeInUTC(),
    @Version
    override val version: Long = 0,
) : TenantUpdatableBaseEntity {
    fun getTimeZoneAsObject(): TimeZone = TimeZone.getTimeZone(timeZone)
}
