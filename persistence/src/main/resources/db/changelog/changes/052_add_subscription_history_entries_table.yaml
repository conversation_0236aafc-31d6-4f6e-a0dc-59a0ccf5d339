databaseChangeLog:
  - changeSet:
      id: 52
      author: system
      changes:
        - createTable:
            tableName: subscription_history_entries
            columns:
              - column:
                  name: number
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: subscription_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: processing_status
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: additional_information
                  type: varchar(1000)
                  constraints:
                    nullable: true
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: id
                  type: uuid
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: created_at
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_subscription_history_entries
            tableName: subscription_history_entries
        - addForeignKeyConstraint:
            baseColumnNames: subscription_id
            baseTableName: subscription_history_entries
            constraintName: fk_subscription_history_entries_subscription_id
            referencedColumnNames: id
            referencedTableName: subscriptions
        - addForeignKeyConstraint:
            baseColumnNames: tenant_id
            baseTableName: subscription_history_entries
            constraintName: fk_subscription_history_entries_tenant_id
            referencedColumnNames: id
            referencedTableName: tenants
        - createIndex:
            indexName: idx_subscription_history_entries_subscription_id
            tableName: subscription_history_entries
            columns:
              - column:
                  name: subscription_id
        - createIndex:
            indexName: idx_subscription_history_entries_tenant_id
            tableName: subscription_history_entries
            columns:
              - column:
                  name: tenant_id
        - createIndex:
            indexName: idx_subscription_history_entries_processing_status
            tableName: subscription_history_entries
            columns:
              - column:
                  name: processing_status
