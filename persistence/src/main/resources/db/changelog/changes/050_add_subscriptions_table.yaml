databaseChangeLog:
  - changeSet:
      id: 50
      author: ai-assistant
      changes:
        - createTable:
            tableName: subscriptions
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: created_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_at
                  type: datetime
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: bigint
                  constraints:
                    nullable: false
              - column:
                  name: tenant_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: key
                  type: varchar(100)
                  constraints:
                    nullable: false
              - column:
                  name: name
                  type: varchar(200)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(500)
                  constraints:
                    nullable: true
              - column:
                  name: customer_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: varchar(20)
                  constraints:
                    nullable: false
              - column:
                  name: frequency
                  type: varchar(20)
                  constraints:
                    nullable: false
              - column:
                  name: amount
                  type: decimal(19, 4)
                  constraints:
                    nullable: false
              - column:
                  name: currency
                  type: char(3)
                  constraints:
                    nullable: false
              - column:
                  name: start_date
                  type: date
                  constraints:
                    nullable: false
              - column:
                  name: end_date
                  type: date
                  constraints:
                    nullable: true
              - column:
                  name: next_billing_date
                  type: date
                  constraints:
                    nullable: false
        - addUniqueConstraint:
            columnNames: tenant_id,key
            constraintName: tenant_key_unique_subscriptions
            tableName: subscriptions
        - addForeignKeyConstraint:
            baseColumnNames: customer_id
            baseTableName: subscriptions
            constraintName: fk_subscriptions_customer
            referencedColumnNames: id
            referencedTableName: customers
