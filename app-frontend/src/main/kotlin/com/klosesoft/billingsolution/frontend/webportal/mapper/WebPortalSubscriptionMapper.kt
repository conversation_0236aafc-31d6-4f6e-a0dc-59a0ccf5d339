package com.klosesoft.billingsolution.frontend.webportal.mapper

import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.generated.api.webportal.server.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.PageDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.SubscriptionFrequencyDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.SubscriptionStatusDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionListEntryDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSubscriptionsPagedResponseDto
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class WebPortalSubscriptionMapper {

    suspend fun toWebPortalPagedSubscriptionsPageResponse(
        subscriptionsPage: ReactivePageImpl<SubscriptionDomainDto>,
    ): WebPortalSubscriptionsPagedResponseDto = WebPortalSubscriptionsPagedResponseDto(
        pageData = PageDataDto(totalCount = subscriptionsPage.totalElements, pageIndex = subscriptionsPage.number),
        content = subscriptionsPage.map { subscription -> this.toWebPortalListDto(subscription) }.content.toList(),
    )

    suspend fun toWebPortalListDto(
        subscription: SubscriptionDomainDto,
    ): WebPortalSubscriptionListEntryDto = WebPortalSubscriptionListEntryDto(
        key = subscription.key,
        name = subscription.name,
        customerKey = subscription.customerKey,
        customerName = subscription.customerKey, // TODO: Load actual customer name
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        nextBillingDate = subscription.nextBillingDate,
        createdAt = WebPortalMapperUtil.toOffSetDateTime(subscription.startDate.atStartOfDay()),
    )

    suspend fun toWebPortalSubscriptionDetailsResponse(
        subscription: SubscriptionDomainDto,
    ): WebPortalSubscriptionDetailsResponseDto = WebPortalSubscriptionDetailsResponseDto(
        subscriptionData = toWebPortalSubscriptionDetailsData(subscription),
    )

    private fun toWebPortalSubscriptionDetailsData(
        subscription: SubscriptionDomainDto,
    ): WebPortalSubscriptionDetailsDataDto = WebPortalSubscriptionDetailsDataDto(
        key = subscription.key,
        name = subscription.name,
        description = subscription.description,
        customerKey = subscription.customerKey,
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        startDate = subscription.startDate,
        endDate = subscription.endDate,
        nextBillingDate = subscription.nextBillingDate,
        version = subscription.version,
    )

    fun toDomainDto(
        webPortalSubscriptionDetailsDataDto: WebPortalSubscriptionDetailsDataDto,
    ): SubscriptionDomainDto = SubscriptionDomainDto(
        key = webPortalSubscriptionDetailsDataDto.key,
        name = webPortalSubscriptionDetailsDataDto.name,
        description = webPortalSubscriptionDetailsDataDto.description,
        customerKey = webPortalSubscriptionDetailsDataDto.customerKey,
        status = SubscriptionStatus.valueOf(webPortalSubscriptionDetailsDataDto.status.name),
        frequency = SubscriptionFrequency.valueOf(webPortalSubscriptionDetailsDataDto.frequency.name),
        amount = webPortalSubscriptionDetailsDataDto.amount,
        currency = Currency.valueOf(webPortalSubscriptionDetailsDataDto.currency.name),
        startDate = webPortalSubscriptionDetailsDataDto.startDate,
        endDate = webPortalSubscriptionDetailsDataDto.endDate,
        nextBillingDate = webPortalSubscriptionDetailsDataDto.nextBillingDate,
        version = webPortalSubscriptionDetailsDataDto.version ?: 0,
    )
}
