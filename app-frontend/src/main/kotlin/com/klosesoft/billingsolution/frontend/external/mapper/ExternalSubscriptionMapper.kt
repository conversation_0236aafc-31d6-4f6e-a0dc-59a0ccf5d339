package com.klosesoft.billingsolution.frontend.external.mapper

import com.klosesoft.billingsolution.domain.model.ReactivePageImpl
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.generated.api.external.server.model.CurrencyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionListEntryDto
import com.klosesoft.billingsolution.generated.api.external.server.model.ExternalSubscriptionsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.external.server.model.PageDataDto
import com.klosesoft.billingsolution.generated.api.external.server.model.SubscriptionFrequencyDto
import com.klosesoft.billingsolution.generated.api.external.server.model.SubscriptionStatusDto
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import java.time.ZoneId

@Service
class ExternalSubscriptionMapper {

    suspend fun toExternalPagedResponse(
        subscriptionsPage: ReactivePageImpl<SubscriptionDomainDto>,
    ): ExternalSubscriptionsPagedResponseDto = ExternalSubscriptionsPagedResponseDto(
        pageData = PageDataDto(totalCount = subscriptionsPage.totalElements, pageIndex = subscriptionsPage.number),
        content = subscriptionsPage.map { subscription -> this.toExternalListDto(subscription) }.content.toList(),
    )

    suspend fun toExternalListDto(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionListEntryDto = ExternalSubscriptionListEntryDto(
        key = subscription.key,
        name = subscription.name,
        customerKey = subscription.customerKey,
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        nextBillingDate = subscription.nextBillingDate,
        createdAt = subscription.startDate.atStartOfDay().atZone(ZoneId.of("UTC")).toOffsetDateTime(),
    )

    suspend fun toExternalSubscriptionDetailsResponse(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionDetailsResponseDto = ExternalSubscriptionDetailsResponseDto(
        subscriptionData = toExternalSubscriptionDetailsData(subscription),
    )

    private fun toExternalSubscriptionDetailsData(
        subscription: SubscriptionDomainDto,
    ): ExternalSubscriptionDetailsDataDto = ExternalSubscriptionDetailsDataDto(
        key = subscription.key,
        name = subscription.name,
        description = subscription.description,
        customerKey = subscription.customerKey,
        status = SubscriptionStatusDto.valueOf(subscription.status.name),
        frequency = SubscriptionFrequencyDto.valueOf(subscription.frequency.name),
        amount = subscription.amount,
        currency = CurrencyDto.valueOf(subscription.currency.name),
        startDate = subscription.startDate,
        endDate = subscription.endDate,
        nextBillingDate = subscription.nextBillingDate,
        version = subscription.version,
    )

    fun toDomainDto(
        externalSubscriptionDetailsDataDto: ExternalSubscriptionDetailsDataDto,
    ): SubscriptionDomainDto = SubscriptionDomainDto(
        key = externalSubscriptionDetailsDataDto.key,
        name = externalSubscriptionDetailsDataDto.name,
        description = externalSubscriptionDetailsDataDto.description,
        customerKey = externalSubscriptionDetailsDataDto.customerKey,
        status = SubscriptionStatus.valueOf(externalSubscriptionDetailsDataDto.status.name),
        frequency = SubscriptionFrequency.valueOf(externalSubscriptionDetailsDataDto.frequency.name),
        amount = externalSubscriptionDetailsDataDto.amount,
        currency = Currency.valueOf(externalSubscriptionDetailsDataDto.currency.name),
        startDate = externalSubscriptionDetailsDataDto.startDate,
        endDate = externalSubscriptionDetailsDataDto.endDate,
        nextBillingDate = externalSubscriptionDetailsDataDto.nextBillingDate,
        version = externalSubscriptionDetailsDataDto.version ?: 0,
    )
}
