package com.klosesoft.billingsolution.databasefiller.creator

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.StorageType
import com.klosesoft.billingsolution.persistence.model.entity.BusinessSegment
import com.klosesoft.billingsolution.persistence.model.entity.Tenant
import com.klosesoft.billingsolution.persistence.model.entity.TenantConfig
import java.math.BigDecimal
import java.util.Locale
import java.util.UUID

object TenantCreator {
    fun createBusinessSegment(
        tenantId: UUID,
        key: String,
        invoiceNumberPattern: String,
        logo: ByteArray,
        originatorAddressId: UUID,
        vatId: String,
    ): BusinessSegment {
        val businessSegment =
            BusinessSegment(
                key = key,
                invoiceNumberPattern = invoiceNumberPattern,
                tenantId = tenantId,
                createdBy = BillingSolutionConstants.SYSTEM_USER_ID,
                lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
                logo = logo,
                originatorAddressId = originatorAddressId,
                vatId = vatId,
            )

        return businessSegment
    }

    fun createActiveTenant(
        key: String,
    ): Tenant {
        val tenant =
            Tenant(
                key = key,
                active = true,
                createdBy = BillingSolutionConstants.SYSTEM_USER_ID,
                lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            )

        return tenant
    }

    fun createTenantConfig(
        tenantId: UUID,
        appClientId: String,
        orderWorkflow: String,
        subscriptionWorkflow: String = BillingSolutionConstants.STANDARD_SUBSCRIPTION_WORKFLOW,
        ledgerCurrency: Currency,
        features: List<Feature>,
        locale: Locale,
        theme: String,
        timeZone: String,
        defaultLanguage: Language,
        defaultTaxRate: BigDecimal,
    ): TenantConfig {
        val tenantConfig =
            TenantConfig(
                appClientId = appClientId,
                storageType = StorageType.S3,
                orderWorkflow = orderWorkflow,
                subscriptionWorkflow = subscriptionWorkflow,
                ledgerCurrency = ledgerCurrency,
                features = features,

                key = tenantId.toString() + "_config",
                tenantId = tenantId,
                createdBy = BillingSolutionConstants.SYSTEM_USER_ID,
                lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
                locale = locale.toLanguageTag(),
                theme = theme,
                timeZone = timeZone,
                defaultLanguage = defaultLanguage,
                defaultTaxRate = defaultTaxRate,
            )

        return tenantConfig
    }
}
