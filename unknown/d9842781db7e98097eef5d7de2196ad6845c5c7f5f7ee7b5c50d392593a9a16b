package com.klosesoft.billingsolution.persistence.service

import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionHistoryEntry
import com.klosesoft.billingsolution.persistence.repository.SubscriptionHistoryEntryRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class SubscriptionHistoryEntryLoadService(
    r2dbcEntityTemplate: R2dbcEntityTemplate,
) : AbstractLoadService<SubscriptionHistoryEntry>(r2dbcEntityTemplate, SubscriptionHistoryEntry::class.java) {

    @Autowired
    private lateinit var subscriptionHistoryEntryRepository: SubscriptionHistoryEntryRepository

    suspend fun findMaxNumberBySubscriptionId(
        subscriptionId: UUID,
    ): Long {
        return subscriptionHistoryEntryRepository.findTop1BySubscriptionIdOrderByNumberDesc(subscriptionId) ?: 0
    }
}
