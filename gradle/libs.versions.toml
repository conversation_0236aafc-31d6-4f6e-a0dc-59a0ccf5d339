# https://docs.gradle.org/current/userguide/platforms.html#sub::toml-dependencies-format

[versions]
spring-boot = "3.5.3"
io-swagger-core-v3-swagger-annotations = "2.2.34"
org-openapitools-jackson-databind-nullable = "0.2.6"
software-amazon-awssdk = "2.31.70"
jjwt = "0.12.6"
jasperreports = "7.0.3"
jirztka-rsql-parser = "2.1.0"
retrofit2 = "3.0.0"
shedlock = "6.9.0"
okhttp = "4.12.0"
kotlin-logging = "7.0.7"
okio = "3.13.0"
flowable = "7.1.0"
swift-mt = "SRU2023-10.1.16"
jsonata = "0.9.8"
zxing = "3.5.3"
mustang = "2.17.0"
xmlunit = "2.10.3"
dom4j = "2.1.4"
commons-io = "2.19.0"

# Tests
mockk = "1.14.4"
springmockk = "4.0.2"
archunit = "1.3.0"
cucumber = "7.23.0"
pdfbox = "3.0.5"
kotest = "5.9.1"

kotlin = "2.0.21"

# Plugins
# https://plugins.gradle.org/
spring-dependency-management = "1.1.7"
test-logger = "4.0.0"
gradle-ktlint = "12.3.0"
openapi-generator = "7.8.0" # TODO 7.14.0
gradle-docker-compose = "0.17.12"
gradle-cucumber = "0.0.12"
gradle-cluecumber = "1.4.0"
versions-plugin = "0.52.0"
jaxb = "7.0.2"
jib = "3.4.1"
detekt = "1.23.8"

[libraries]
spring-boot-starter-cache = { module = "org.springframework.boot:spring-boot-starter-cache" }
spring-boot-starter-webflux = { module = "org.springframework.boot:spring-boot-starter-webflux" }
spring-boot-starter-data-r2dbc = { module = "org.springframework.boot:spring-boot-starter-data-r2dbc" }
spring-boot-starter-data-jdbc = { module = "org.springframework.boot:spring-boot-starter-data-jdbc" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation" }
spring-boot-starter-security = { module = "org.springframework.boot:spring-boot-starter-security" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
org-springframework-retry-spring-retry = { module = "org.springframework.retry:spring-retry" }
spring-boot-starter-flowable = { module = "org.flowable:flowable-spring-boot-starter-process", version.ref = "flowable" }

postgresql = { module = "org.postgresql:postgresql" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine" }
io-swagger-core-v3-swagger-annotations = { module = "io.swagger.core.v3:swagger-annotations", version.ref = "io-swagger-core-v3-swagger-annotations" }
org-liquibase-liquibase-core = { module = "org.liquibase:liquibase-core" }
org-openapitools-jackson-databind-nullable = { module = "org.openapitools:jackson-databind-nullable", version.ref = "org-openapitools-jackson-databind-nullable" }
net-sf-jasperreports = { module = "net.sf.jasperreports:jasperreports", version.ref = "jasperreports" }
net-sf-jasperreports-fonts = { module = "net.sf.jasperreports:jasperreports-fonts", version.ref = "jasperreports" }
net-sf-jasperreports-spring = { module = "net.sf.jasperreports:jasperreports-spring", version.ref = "jasperreports" }
net-sf-jasperreports-pdf = { module = "net.sf.jasperreports:jasperreports-pdf", version.ref = "jasperreports" }
net-sf-jasperreports-ejbql = { module = "net.sf.jasperreports:jasperreports-ejbql", version.ref = "jasperreports" }
swift-mt = { module = "com.prowidesoftware:pw-swift-core", version.ref = "swift-mt" }
zxing = { module = "com.google.zxing:core", version.ref = "zxing" }
mustang = { module = "org.mustangproject:library", version.ref = "mustang" }
xmlunit = { module = "org.xmlunit:xmlunit-assertj", version.ref = "xmlunit" }
dom4j = { module = "org.dom4j:dom4j", version.ref = "dom4j" }
commons-io = { module = "commons-io:commons-io", version.ref = "commons-io" }

jaxb-runtime = { module = "org.glassfish.jaxb:jaxb-runtime" }
jakarta-activation-api = { module = "jakarta.activation:jakarta.activation-api" }
jakarta-xml-bind-api = { module = "jakarta.xml.bind:jakarta.xml.bind-api" }

jjwt-api = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jjwt-jackson = { module = "io.jsonwebtoken:jjwt-jackson", version.ref = "jjwt" }

jsonata = { module = "com.dashjoin:jsonata", version.ref = "jsonata" }

software-amazon-awssdk-s3 = { module = "software.amazon.awssdk:s3", version.ref = "software-amazon-awssdk" }
software-amazon-awssdk-cognito = { module = "software.amazon.awssdk:cognitoidentityprovider", version.ref = "software-amazon-awssdk" }

jirutka-rsql-parser = { module = "cz.jirutka.rsql:rsql-parser", version.ref = "jirztka-rsql-parser" }

retrofit2-scalars = { module = "com.squareup.retrofit2:converter-scalars", version.ref = "retrofit2" }
retrofit2-jackson = { module = "com.squareup.retrofit2:converter-jackson", version.ref = "retrofit2" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }

shedlock-spring = { module = "net.javacrumbs.shedlock:shedlock-spring", version.ref = "shedlock" }
shedlock-r2dbc = { module = "net.javacrumbs.shedlock:shedlock-provider-r2dbc", version.ref = "shedlock" }

r2dbc-pool = { module = "io.r2dbc:r2dbc-pool" }
r2dbc-postgresql = { module = "org.postgresql:r2dbc-postgresql" }

angus-mail = { module = "org.eclipse.angus:angus-mail" }

jackson-dataformat-xml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-xml" }
jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin" }

kotlin-logging = { module = "io.github.oshai:kotlin-logging", version.ref = "kotlin-logging" }
kotlinx-coroutines-jdk8 = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8" }
kotlinx-coroutines-reactor = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-reactor" }
kotlinx-coroutines-reactive = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-reactive" }
kotlinx-coroutines-slf4j = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j" }

okio-jvm = { module = "com.squareup.okio:okio-jvm", version.ref = "okio" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }

# Unit / Integration Tests
mockk = { module = "io.mockk:mockk-jvm", version.ref = "mockk" }
springmockk = { module = "com.ninja-squad:springmockk", version.ref = "springmockk" }
archunit = { module = "com.tngtech.archunit:archunit-junit5", version.ref = "archunit" }
kotest-junit = { module = "io.kotest:kotest-runner-junit5", version.ref = "kotest" }
kotest-assertions = { module = "io.kotest:kotest-assertions-core", version.ref = "kotest" }
kotest-property = { module = "io.kotest:kotest-property", version.ref = "kotest" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }

# Acceptance Tests
cucumber-spring = { module = "io.cucumber:cucumber-spring", version.ref = "cucumber" }
cucumber-java8 = { module = "io.cucumber:cucumber-java8", version.ref = "cucumber" }
cucumber-junit = { module = "io.cucumber:cucumber-junit", version.ref = "cucumber" }
pdfbox = { module = "org.apache.pdfbox:pdfbox", version.ref = "pdfbox" }
xmpbox = { module = "org.apache.pdfbox:xmpbox", version.ref = "pdfbox" }
preflight = { module = "org.apache.pdfbox:preflight", version.ref = "pdfbox" }

[bundles]
impl-network = ["retrofit2-scalars", "retrofit2-jackson", "okio-jvm", "okio", "okhttp"]
impl-shedlock = ["shedlock-spring", "shedlock-r2dbc"]
impl-coroutines = ["kotlinx-coroutines-jdk8", "kotlinx-coroutines-reactor", "kotlinx-coroutines-reactive", "kotlinx-coroutines-slf4j"]

test-mockk = ["mockk", "springmockk"]
test-cucumber = ["cucumber-spring", "cucumber-java8", "cucumber-junit"]

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }

spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }

test-logger = { id = "com.adarshr.test-logger", version.ref = "test-logger" }
gradle-ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "gradle-ktlint" }
openapi-generator = { id = "org.openapi.generator", version.ref = "openapi-generator" }
gradle-docker-compose = { id = "com.avast.gradle.docker-compose", version.ref = "gradle-docker-compose" }

gradle-cucumber = { id = "se.thinkcode.cucumber-runner", version.ref = "gradle-cucumber" }
gradle-cluecumber = { id = "de.javaansehz.cluecumber-report-gradle-plugin", version.ref = "gradle-cluecumber" }
versions-plugin = { id = "com.github.ben-manes.versions", version.ref = "versions-plugin" }
gradle-jaxb = { id = "com.intershop.gradle.jaxb", version.ref = "jaxb" }
gradle-jib = { id = "com.google.cloud.tools.jib", version.ref = "jib" }

detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
