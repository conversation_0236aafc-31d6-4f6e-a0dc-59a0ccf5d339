package com.klosesoft.billingsolution.domain.model.valueobject

enum class ProcessingStatus {
    INIT,
    ORDER_CREATED,

    CREDIT_NOTE_CREATED,
    CREDIT_NOTE_POSTING_RECORD_CREATED,
    CREDIT_NOTE_PAYOUT_CREATED,
    CREDIT_NOTE_PAYOUT_POSTING_RECORD_CREATED,

    DEPOSIT_INVOICE_CREATED,
    DEPOSIT_PAYMENT_POSTING_RECORD_CREATED,
    DEPOSIT_POSTING_RECORD_CREATED,
    DEPOSIT_PAYMENT_DEBITED,

    FINAL_INVOICE_CREATED,
    FINAL_INVOICE_CREDIT_ONLY_CREATED,
    FINAL_INVOICE_DEBIT_ONLY_CREATED,
    FINAL_INVOICE_PAYMENT_POSTING_RECORD_CREATED,
    FINAL_INVOICE_POSTING_RECORD_CREATED,
    FINAL_INVOICE_PAYMENT_DEBITED,

    FINAL_SELFBILLING_INVOICE_CREATED,
    FINAL_SELFBILLING_INVOICE_REVERSED_CREATED,
    FINAL_SELFBILLING_INVOICE_POSTING_RECORD_CREATED,
    FINAL_SELFBILLING_INVOICE_REVERSED_POSTING_RECORD_CREATED,

    FINAL_COMMISSION_INVOICE_CREATED,
    FINAL_COMMISSION_INVOICE_REVERSED_CREATED,
    FINAL_COMMISSION_INVOICE_POSTING_RECORD_CREATED,
    FINAL_COMMISSION_INVOICE_REVERSED_POSTING_RECORD_CREATED,

    ORDER_UPDATED,

    PAYMENT_ASSIGNMENT_CREATED,
    PAYMENT_ASSIGNMENT_POSTING_RECORD_CREATED,

    CANCELLATION_APPROVAL_CREATED,
    CANCELLATION_APPROVED,
    CANCELLATION_REJECTED,

    CUSTOMER_EMAIL_SENT,

    // Subscription-specific processing statuses
    SUBSCRIPTION_CREATED,
    SUBSCRIPTION_ACTIVATED,
    SUBSCRIPTION_BILLING_PROCESSED,
    SUBSCRIPTION_INVOICE_CREATED,
    SUBSCRIPTION_INVOICE_SENT,
    SUBSCRIPTION_PAUSED,
    SUBSCRIPTION_RESUMED,
    SUBSCRIPTION_CANCELLED,
    SUBSCRIPTION_EXPIRED,
    SUBSCRIPTION_UPDATED,
}
