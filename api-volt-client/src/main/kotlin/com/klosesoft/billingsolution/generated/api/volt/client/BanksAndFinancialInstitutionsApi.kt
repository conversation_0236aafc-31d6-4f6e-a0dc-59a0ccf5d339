package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.BankFullDto

interface BanksAndFinancialInstitutionsApi {
    /**
     * Bank details
     * View details about a single bank or financial institution
     * Responses:
     *  - 200: Details of bank or financial institution
     *  - 401: 
     *  - 403: 
     *  - 404: 
     *
     * @param id 
     * @return [BankFullDto]
     */
    @GET("banks/{id}")
    suspend fun getBankById(@Path("id") id: kotlin.String): Response<BankFullDto>

}
