package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.CreateDropinPayment201ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreateDropinPaymentRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayment201ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetPaymentsPaymentIdNameMatchScore200ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PaymentDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PaymentFullDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PaymentListDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PaymentListStatsDto

interface PaymentsApi {
    /**
     * New Embedded checkout
     * The first step is to submit the details of the payment you&#39;d like to initiate. We&#39;ll validate that all the required data is supplied and that it&#39;s a payment that Volt can support.v2.  Step-by-step instructions for creating Embedded Checkout are available in our [product guides](https://docs.volt.io/gateway/embedded-checkout-v1/embedded-checkout-v2/) area. 
     * Responses:
     *  - 201: **Created** - The payment request was validated and accepted and a payment ID returned 
     *  - 400: 
     *  - 401: 
     *  - 403: 
     *
     * @param createDropinPaymentRequestDto Details of the payment you&#39;d like to initiate (optional)
     * @return [CreateDropinPayment201ResponseDto]
     */
    @POST("dropin")
    suspend fun createDropinPayment(@Body createDropinPaymentRequestDto: CreateDropinPaymentRequestDto? = null): Response<CreateDropinPayment201ResponseDto>

    /**
     * New payment request
     * The first step is to submit the details of the payment you&#39;d like to initiate.  We&#39;ll validate that all the required data is supplied and that it&#39;s a payment that Volt can support.  Step-by-step instructions for payment initiation are available in our [product guides](https://docs.volt.io/gateway/hosted/) area. 
     * Responses:
     *  - 201: **Created** - The payment request was validated and accepted and a payment ID returned 
     *  - 400: 
     *  - 401: 
     *  - 403: 
     *
     * @param paymentDto Details of the payment you&#39;d like to initiate (optional)
     * @return [CreatePayment201ResponseDto]
     */
    @POST("v2/payments")
    suspend fun createPayment(@Body paymentDto: PaymentDto? = null): Response<CreatePayment201ResponseDto>


    /**
    * enum for parameter type
    */
    enum class TypeFuzeboxGetPaymentListStats(val value: kotlin.String) {
        @JsonProperty(value = "BILL") BILL("BILL"),
        @JsonProperty(value = "GOODS") GOODS("GOODS"),
        @JsonProperty(value = "PERSON_TO_PERSON") PERSON_TO_PERSON("PERSON_TO_PERSON"),
        @JsonProperty(value = "OTHER") OTHER("OTHER"),
        @JsonProperty(value = "SERVICES") SERVICES("SERVICES")
    }

    /**
     * Payments list statistics
     * This endpoint retrieves payment statistics based on specified filters.  **For now:** This endpoint retrieves the count of payments. It does not return detailed payment information; instead, it provides an aggregated count based on the filter criteria.
     * Responses:
     *  - 200: **OK** - All payments list statistics corresponding to specified filters 
     *  - 401: 
     *  - 403: 
     *
     * @param id UUID of the Payment (optional)
     * @param bank UUID of the Bank (optional)
     * @param currency 3-letter currency code - See [ISO 4217 - Currency codes](https://www.iso.org/iso-4217-currency-codes.html) (optional)
     * @param amount The amount of the transaction in 1/100 units (pence, cents etc) (optional)
     * @param type The transaction type (these are predefined) (optional)
     * @param uniqueReference Unique reference for the payment (optional)
     * @param status The current status of the payment (optional)
     * @return [PaymentListStatsDto]
     */
    @GET("fuzebox/payments/stats")
    suspend fun fuzeboxGetPaymentListStats(@Query("id") id: java.util.UUID? = null, @Query("bank") bank: java.util.UUID? = null, @Query("currency") currency: kotlin.String? = null, @Query("amount") amount: kotlin.Int? = null, @Query("type") type: TypeFuzeboxGetPaymentListStats? = null, @Query("uniqueReference") uniqueReference: kotlin.String? = null, @Query("status") status: kotlin.String? = null): Response<PaymentListStatsDto>


    /**
    * enum for parameter type
    */
    enum class TypeFuzeboxListPayments(val value: kotlin.String) {
        @JsonProperty(value = "BILL") BILL("BILL"),
        @JsonProperty(value = "GOODS") GOODS("GOODS"),
        @JsonProperty(value = "PERSON_TO_PERSON") PERSON_TO_PERSON("PERSON_TO_PERSON"),
        @JsonProperty(value = "OTHER") OTHER("OTHER"),
        @JsonProperty(value = "SERVICES") SERVICES("SERVICES")
    }

    /**
     * List payments using paginator without count
     * Fetch list of payments based on specific selection criteria.  *It&#39;s a duplicate of* **_/payments** *with added &#x60;has-next&#x60; and &#x60;has-prev&#x60; in paginator, instead of using &#x60;count&#x60;. This change was made to improve performance.*
     * Responses:
     *  - 200: **OK** - All payments corresponding to specified filters 
     *  - 401: 
     *  - 403: 
     *
     * @param id UUID of the Payment (optional)
     * @param bank UUID of the Bank (optional)
     * @param currency 3-letter currency code - See [ISO 4217 - Currency codes](https://www.iso.org/iso-4217-currency-codes.html) (optional)
     * @param amount The amount of the transaction in 1/100 units (pence, cents etc) (optional)
     * @param type The transaction type (these are predefined) (optional)
     * @param uniqueReference Unique reference for the payment (optional)
     * @param status The current status of the payment (optional)
     * @param page You can request additional pages of information by appending the page parameter to your GET request. Pages start numbering at 1 and are in blocks of 50 records, so to call records 51-100, append **page**&#x3D;2 to your request. If you omit the page parameter, we will return you the information on page 1.  (optional, default to 1)
     * @param limit If you want to return more than the default number of records per page, append the parameter items to your GET request. To get the first 100 records, append **limit**&#x3D;100 to your request. To get the next 100 records, append **page**&#x3D;2&amp;**limit**&#x3D;100. If you omit the items parameter, we will return 50 records per page.  (optional, default to 50)
     * @return [kotlin.collections.List<PaymentListDto>]
     */
    @GET("fuzebox/payments")
    suspend fun fuzeboxListPayments(@Query("id") id: java.util.UUID? = null, @Query("bank") bank: java.util.UUID? = null, @Query("currency") currency: kotlin.String? = null, @Query("amount") amount: kotlin.Int? = null, @Query("type") type: TypeFuzeboxListPayments? = null, @Query("uniqueReference") uniqueReference: kotlin.String? = null, @Query("status") status: kotlin.String? = null, @Query("page") page: kotlin.Int? = 1, @Query("limit") limit: kotlin.Int? = 50): Response<kotlin.collections.List<PaymentListDto>>

    /**
     * Payment details
     * Returns the full details of an existing payment that you&#39;ve created througn the POST /payments endpoint.  Simply supply the &#x60;&#x60;&#x60;id&#x60;&#x60;&#x60; of the payment we returned when the payment was created.  You can retrieve a filterable list of payments through the &#x60;&#x60;&#x60;GET /payments&#x60;&#x60;&#x60; endpoint. 
     * Responses:
     *  - 200: **OK** - Payment details for the ID specified 
     *  - 401: 
     *  - 403: 
     *  - 404: 
     *
     * @param id ID of the payment
     * @return [PaymentFullDto]
     */
    @GET("payments/{id}")
    suspend fun getPaymentById(@Path("id") id: java.util.UUID): Response<PaymentFullDto>


    /**
    * enum for parameter type
    */
    enum class TypeGetPaymentList(val value: kotlin.String) {
        @JsonProperty(value = "BILL") BILL("BILL"),
        @JsonProperty(value = "GOODS") GOODS("GOODS"),
        @JsonProperty(value = "PERSON_TO_PERSON") PERSON_TO_PERSON("PERSON_TO_PERSON"),
        @JsonProperty(value = "OTHER") OTHER("OTHER"),
        @JsonProperty(value = "SERVICES") SERVICES("SERVICES")
    }

    /**
     * List payments
     * Reporting API available to fetch list of payments based on specific selection criteria
     * Responses:
     *  - 200: **OK** - All payments corresponding to specified filters 
     *  - 401: 
     *  - 403: 
     *
     * @param id UUID of the Payment (optional)
     * @param bank UUID of the Bank (optional)
     * @param currency 3-letter currency code - See [ISO 4217 - Currency codes](https://www.iso.org/iso-4217-currency-codes.html) (optional)
     * @param amount The amount of the transaction in 1/100 units (pence, cents etc) (optional)
     * @param type The transaction type (these are predefined) (optional)
     * @param uniqueReference Unique reference for the payment (optional)
     * @param status The current status of the payment (optional)
     * @param page You can request additional pages of information by appending the page parameter to your GET request. Pages start numbering at 1 and are in blocks of 50 records, so to call records 51-100, append **page**&#x3D;2 to your request. If you omit the page parameter, we will return you the information on page 1.  (optional, default to 1)
     * @param limit If you want to return more than the default number of records per page, append the parameter items to your GET request. To get the first 100 records, append **limit**&#x3D;100 to your request. To get the next 100 records, append **page**&#x3D;2&amp;**limit**&#x3D;100. If you omit the items parameter, we will return 50 records per page.  (optional, default to 50)
     * @return [kotlin.collections.List<PaymentListDto>]
     */
    @GET("payments")
    suspend fun getPaymentList(@Query("id") id: java.util.UUID? = null, @Query("bank") bank: java.util.UUID? = null, @Query("currency") currency: kotlin.String? = null, @Query("amount") amount: kotlin.Int? = null, @Query("type") type: TypeGetPaymentList? = null, @Query("uniqueReference") uniqueReference: kotlin.String? = null, @Query("status") status: kotlin.String? = null, @Query("page") page: kotlin.Int? = 1, @Query("limit") limit: kotlin.Int? = 50): Response<kotlin.collections.List<PaymentListDto>>

    /**
     * Payment Match Score
     * This endpoint will allow accurately matches naming nuances between merchant inputs and end-user bank records
     * Responses:
     *  - 200: OK
     *  - 403: **Not authorised** - Although your credentials are correct, your access to this section of the API has been disabled or limited. Subsequent requests to this endpoint (even with valid data) will not be processed.
     *  - 404: **Not found** - The resource that you are requesting cannot be found. The response may contain further information.
     *  - 409: **Conflict** - Name match score is only available for payments in the received status.
     *
     * @param paymentId 
     * @param allowInitials default: true - if set to false, receiving an account holder name that contains an initial instead of a first name will significantly impact the match score. (optional)
     * @return [GetPaymentsPaymentIdNameMatchScore200ResponseDto]
     */
    @GET("payments/{paymentId}/name-match-score")
    suspend fun getPaymentsPaymentIdNameMatchScore(@Path("paymentId") paymentId: kotlin.String, @Query("allow_initials") allowInitials: kotlin.Boolean? = null): Response<GetPaymentsPaymentIdNameMatchScore200ResponseDto>

}
