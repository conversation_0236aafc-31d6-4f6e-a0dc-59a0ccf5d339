package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.OAuthAuthentication200ResponseDto

interface AuthenticatingWithTheAPIApi {
    /**
     * oAuth2 password authentication
     * We use the industry-standard oAuth2 authentication to secure our API.  ### Information you&#39;ll need - Your **Client ID** and **Client Secret** (from the application you created in Fuzebox) - Your **API username** and **password** (from the merchant credentials section in Fuzebox)  Step-by-step instructions on how to authenticate are available in our [product guides](https://docs.volt.io/gateway/authentication/]) area. 
     * Responses:
     *  - 200: **OK** - Successful login attempt, returning an access token 
     *  - 401: 
     *
     * @param grantType This field **must** have a value of **password**
     * @param clientId The ID for the application you&#39;re using to access the Volt API
     * @param clientSecret The secret for the application you&#39;re using to access the Volt API
     * @param username Username in email address format
     * @param password Password from your credentials
     * @return [OAuthAuthentication200ResponseDto]
     */
    @FormUrlEncoded
    @POST("oauth")
    suspend fun oAuthAuthentication(@Field("grant_type") grantType: kotlin.String, @Field("client_id") clientId: java.util.UUID, @Field("client_secret") clientSecret: java.util.UUID, @Field("username") username: kotlin.String, @Field("password") password: kotlin.String): Response<OAuthAuthentication200ResponseDto>

}
