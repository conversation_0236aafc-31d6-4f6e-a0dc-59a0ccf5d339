package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import okhttp3.ResponseBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.GetReports400ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetReportsId400ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetReportsId404ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetReportsIdDownload404ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsAuditLogRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsConnectRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsPayment400ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsPayment429ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsPaymentRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsPayoutsRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PostReportsRefundRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.ReportDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.ReportRequestedDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.ValidationErrorV1Dto

interface ReportingApi {
    /**
     * Get available reports
     * This endpoint will allow you to fetch a list containing all the reports you&#39;ve generated.  Use the &#x60;&#x60;&#x60;id&#x60;&#x60;&#x60; of the report to download it using the &#x60;&#x60;&#x60;/reports/{id}/download&#x60;&#x60;&#x60; endpoint.  By default, it will return a complete list of reports for your entire customer hierarchy.  Providing a &#x60;&#x60;&#x60;customerId&#x60;&#x60;&#x60; in the query will allow you to limit the list to reports available for one particular merchant. 
     * Responses:
     *  - 200: OK
     *  - 400: Bad Request (e.g. an invalid customerId was provided) 
     *  - 422: 
     *
     * @param customerId The ID of a specific merchant within your customer hierarchy (optional)
     * @return [kotlin.collections.List<ReportDto>]
     */
    @GET("reports")
    suspend fun getReports(@Query("customerId") customerId: java.util.UUID? = null): Response<kotlin.collections.List<ReportDto>>

    /**
     * Get report by ID
     * This endpoint allows fetching the details of a specific report.  Note that this will return the details of the report only.  To download the report as a CSV file, use the &#x60;&#x60;&#x60;/reports/{id}/download&#x60;&#x60;&#x60; endpoint.  Provide the &#x60;&#x60;&#x60;id&#x60;&#x60;&#x60; returned when you requested the report. 
     * Responses:
     *  - 200: OK
     *  - 400: Bad Request (f.e. invalid ID provided)
     *  - 404: Report not found
     *
     * @param id ID of the report you wish to retrieve
     * @return [ReportDto]
     */
    @GET("reports/{id}")
    suspend fun getReportsId(@Path("id") id: java.util.UUID): Response<ReportDto>

    /**
     * Download a report
     * This endpoint allows downloading a previously generated report in CSV format.  Use the &#x60;&#x60;&#x60;id&#x60;&#x60;&#x60; returned when you requested the report. 
     * Responses:
     *  - 200: OK
     *  - 400: Bad Request (e.g. in case of invalid ID provided)
     *  - 404: Report not found or not generated yet
     *
     * @param id ID of report
     * @return [ResponseBody]
     */
    @GET("reports/{id}/download")
    suspend fun getReportsIdDownload(@Path("id") id: java.util.UUID): Response<ResponseBody>

    /**
     * Create Audit Log report
     * This endpoint initiates the generation of an audit log report for a specified time frame. Reports are generated asynchronously and, once ready, can be downloaded as CSV files from &#x60;/reports/{id}/download&#x60;. Use &#x60;/reports/{id}&#x60; to monitor the report&#39;s status.  The period between &#x60;startDate&#x60; and &#x60;endDate&#x60; must not exceed 92 days, allowing for quarterly reports. Specifying a &#x60;customerId&#x60; filters the report to include data exclusively for that merchant. Without a &#x60;customerId&#x60;, the report covers all merchants in your customer hierarchy. 
     * Responses:
     *  - 201: Created
     *  - 400: **Bad Request** - For example, when invalid JSON was provided. 
     *  - 422: 
     *  - 429: **Too Many Requests** - This may occur when you have requested more than 5 reports to be generated at the same time. 
     *
     * @param postReportsAuditLogRequestDto  (optional)
     * @return [ReportRequestedDto]
     */
    @POST("reports/auditlog")
    suspend fun postReportsAuditLog(@Body postReportsAuditLogRequestDto: PostReportsAuditLogRequestDto? = null): Response<ReportRequestedDto>

    /**
     * Create Connect report
     * This endpoint allows you to request a Connect report for a selected &#x60;&#x60;&#x60;bankAccountId&#x60;&#x60;&#x60; and specific period.  Once requested, your report will be generated asynchronously and available at the &#x60;&#x60;&#x60;/reports/id/download endpoint&#x60;&#x60;&#x60; as a CSV file once it&#39;s been generated.  Check on the availability status of your report using the &#x60;&#x60;&#x60;/reports/{id}&#x60;&#x60;&#x60; endpoint.  The &#x60;&#x60;&#x60;startDate&#x60;&#x60;&#x60; and &#x60;&#x60;&#x60;endDate&#x60;&#x60;&#x60; may be a maximum of **92 days** apart, so you can generate reports containing 3 months data. 
     * Responses:
     *  - 201: Created
     *  - 400: **Bad Request** - For example, when invalid JSON was provided. 
     *  - 422: 
     *  - 429: **Too Many Requests** - This may occur when you have requested more than 5 reports to be generated at the same time. 
     *
     * @param bankAccountId Connect bank account ID that you&#39;re requesting report for
     * @param postReportsConnectRequestDto  (optional)
     * @return [ReportRequestedDto]
     */
    @POST("reports/connect/{bankAccountId}")
    suspend fun postReportsConnect(@Path("bankAccountId") bankAccountId: kotlin.String, @Body postReportsConnectRequestDto: PostReportsConnectRequestDto? = null): Response<ReportRequestedDto>

    /**
     * Create payment report
     * This endpoint allows you to request a payment report for a specific period.  Once requested, your report will be generated asynchronously and available at the &#x60;&#x60;&#x60;/reports/id/download endpoint&#x60;&#x60;&#x60; as a CSV file once it&#39;s been generated.  Check on the availability status of your report using the &#x60;&#x60;&#x60;/reports/{id}&#x60;&#x60;&#x60; endpoint.  The &#x60;&#x60;&#x60;startDate&#x60;&#x60;&#x60; and &#x60;&#x60;&#x60;endDate&#x60;&#x60;&#x60; may be a maximum of **92 days** apart, so you can generate reports containing 3 months data.  If you supply a &#x60;&#x60;&#x60;customerId&#x60;&#x60;&#x60; for any merchant within your customer hierarchy, only that merchant&#39;s data will be returned.  If a &#x60;&#x60;&#x60;customer ID&#x60;&#x60;&#x60; is **not** provided, the report will contain data for all merchants within your customer hierarchy. 
     * Responses:
     *  - 201: Created
     *  - 400: **Bad Request** - For example, when invalid JSON was provided. 
     *  - 422: 
     *  - 429: **Too Many Requests** - This may occur when you have requested more than 5 reports to be generated at the same time. 
     *
     * @param postReportsPaymentRequestDto  (optional)
     * @return [ReportRequestedDto]
     */
    @POST("reports/payments")
    suspend fun postReportsPayment(@Body postReportsPaymentRequestDto: PostReportsPaymentRequestDto? = null): Response<ReportRequestedDto>

    /**
     * Create Payouts report
     * This endpoint allows you to request a payouts report for a specific period. Once requested, your report will be generated asynchronously and available at the &#x60;&#x60;&#x60;/reports/id/download&#x60;&#x60;&#x60; endpoint as a CSV file once it&#39;s been generated. Check on the availability status of your report using the &#x60;&#x60;&#x60;/reports/{id}&#x60;&#x60;&#x60; endpoint.  The &#x60;&#x60;&#x60;startDate&#x60;&#x60;&#x60; and &#x60;&#x60;&#x60;endDate&#x60;&#x60;&#x60; may be a maximum of **92 days** apart, so you can generate reports containing 3 months data.  If you supply a &#x60;&#x60;&#x60;customerId&#x60;&#x60;&#x60; for any merchant within your customer hierarchy, only that merchant&#39;s data will be returned.  If a &#x60;&#x60;&#x60;customer ID&#x60;&#x60;&#x60; is **not** provided, the report will contain data for all merchants within your customer hierarchy.  If you supply an &#x60;&#x60;&#x60;accountId&#x60;&#x60;&#x60; for Connect account, only data related to this account will be returned.  If an &#x60;&#x60;&#x60;account ID&#x60;&#x60;&#x60; is **not** provided, the report will contain data related to all Connect accounts. 
     * Responses:
     *  - 201: Created
     *  - 400: **Bad Request** - For example, when invalid JSON was provided. 
     *  - 422: 
     *  - 429: **Too Many Requests** - This may occur when you have requested more than 5 reports to be generated at the same time. 
     *
     * @param postReportsPayoutsRequestDto  (optional)
     * @return [ReportRequestedDto]
     */
    @POST("reports/payouts")
    suspend fun postReportsPayouts(@Body postReportsPayoutsRequestDto: PostReportsPayoutsRequestDto? = null): Response<ReportRequestedDto>

    /**
     * Create Refund report
     * This endpoint allows you to request a refund report for a specific period. Once requested, your report will be generated asynchronously and available at the &#x60;&#x60;&#x60;/reports/id/download endpoint&#x60;&#x60;&#x60; as a CSV file once it&#39;s been generated. Check on the availability status of your report using the &#x60;&#x60;&#x60;/reports/{id}&#x60;&#x60;&#x60; endpoint.  The &#x60;&#x60;&#x60;startDate&#x60;&#x60;&#x60; and &#x60;&#x60;&#x60;endDate&#x60;&#x60;&#x60; may be a maximum of **92 days** apart, so you can generate reports containing 3 months data.  If you supply a &#x60;&#x60;&#x60;customerId&#x60;&#x60;&#x60; for any merchant within your customer hierarchy, only that merchant&#39;s data will be returned.  If a &#x60;&#x60;&#x60;customer ID&#x60;&#x60;&#x60; is **not** provided, the report will contain data for all merchants within your customer hierarchy.  If you supply an &#x60;&#x60;&#x60;accountId&#x60;&#x60;&#x60; for Connect account, only data related to this account will be returned.  If an &#x60;&#x60;&#x60;account ID&#x60;&#x60;&#x60; is **not** provided, the report will contain data related to all Connect accounts. 
     * Responses:
     *  - 201: Created
     *  - 400: **Bad Request** - For example, when invalid JSON was provided. 
     *  - 422: 
     *  - 429: **Too Many Requests** - This may occur when you have requested more than 5 reports to be generated at the same time. 
     *
     * @param postReportsRefundRequestDto  (optional)
     * @return [ReportRequestedDto]
     */
    @POST("reports/refund")
    suspend fun postReportsRefund(@Body postReportsRefundRequestDto: PostReportsRefundRequestDto? = null): Response<ReportRequestedDto>

}
