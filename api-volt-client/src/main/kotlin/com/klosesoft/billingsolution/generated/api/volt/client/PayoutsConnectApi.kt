package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout201ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout400ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout403ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout422ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetPayoutBeneficiariesList200ResponseInnerDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetPayoutById200ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PayoutItemDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PayoutRequestDto

interface PayoutsConnectApi {
    /**
     * New payout request
     * **Payouts are only available when using Volt Connect**  This will create a payout request to send funds from your Connect account to a beneficiary.  For regulatory purposes, it&#39;s a closed-loop payout system, meaning you may only payout funds to a person or organisation you&#39;ve previously received money from using Volt Connect.  ### Usage Pass the details of the payout you&#39;d like to make in the body of the request.  ### Approval Payouts don&#39;t require approval if you create them using the API.  If you need to review payouts before sending, this workflow should take place on your own system before you call this endpoint.  ### More information Full details on payouts can be found in our comprehensive integration guides. 
     * Responses:
     *  - 201: **Created** - The payout request was validated and accepted and a payout ID returned. 
     *  - 400: **Bad request** - The request could not be processed due to one or more errors.  If the exception message does not provide enough information, please contact merchant support for further details. 
     *  - 401: 
     *  - 403: **Forbidden** - Although your credentials are correct, your access to payout functionality on the API has been disabled or restricted.  If the exception message does not provide enough information, please contact merchant support for further details. 
     *  - 409: 
     *  - 422: **Unprocessable Entity** - The payout request could not be completed due to a problem with the beneficiary you're trying to send to.  If the exception message does not provide enough information, please contact merchant support for further details. 
     *
     * @param idempotencyKey Recommended to avoid duplicate payout requests (optional)
     * @param xJWSHeader Optional - applicable if Volt is your Connect partner (optional)
     * @param payoutRequestDto Details of the payout you&#39;d like to create (optional)
     * @return [CreatePayout201ResponseDto]
     */
    @POST("payouts")
    suspend fun createPayout(@Header("idempotency-key") idempotencyKey: java.util.UUID? = null, @Header("X-JWS-Header") xJWSHeader: kotlin.String? = null, @Body payoutRequestDto: PayoutRequestDto? = null): Response<CreatePayout201ResponseDto>

    /**
     * Eligible beneficiaries list
     * **Payouts are only available when using Volt Connect**  For regulatory purposes, it&#39;s a closed-loop payout system, meaning you may only payout funds to a person or organisation you&#39;ve previously received money from using Volt Connect.  This endpoint will return a list of beneficiaries you are allowed send payouts to.  ### Usage The list is paginated by default.  ### More information Full details on payouts can be found in our comprehensive integration guides. 
     * Responses:
     *  - 200: **OK** - A paginated list of eligible beneficiaries. 
     *  - 401: 
     *  - 403: 
     *
     * @param name Name of the beneficiary you&#39;d like to send a payout to (optional)
     * @param accountNumber Domestic account number for the beneficiary you&#39;d like to send a payout to (optional)
     * @param iban iban for the beneficiary you&#39;d like to send a payout to (optional)
     * @param page You can request additional pages of information by appending the page parameter to your GET request. Pages start numbering at 1 and are in blocks of 50 records, so to call records 51-100, append **page**&#x3D;2 to your request. If you omit the page parameter, we will return you the information on page 1.  (optional, default to 1)
     * @param limit If you want to return more than the default number of records per page, append the parameter items to your GET request. To get the first 100 records, append **limit**&#x3D;100 to your request. To get the next 100 records, append **page**&#x3D;2&amp;**limit**&#x3D;100. If you omit the items parameter, we will return 50 records per page.  (optional, default to 50)
     * @return [kotlin.collections.List<GetPayoutBeneficiariesList200ResponseInnerDto>]
     */
    @GET("payouts/beneficiaries")
    suspend fun getPayoutBeneficiariesList(@Query("name") name: kotlin.String? = null, @Query("accountNumber") accountNumber: kotlin.String? = null, @Query("iban") iban: kotlin.String? = null, @Query("page") page: kotlin.Int? = 1, @Query("limit") limit: kotlin.Int? = 50): Response<kotlin.collections.List<GetPayoutBeneficiariesList200ResponseInnerDto>>

    /**
     * Payout details
     * **Payouts are only available when using Volt Connect**  This will return details of the selected payout, with approval update history.  ### Usage Pass the **id** of the payout as part of the URL.  ### More information Full details on payouts can be found in our comprehensive integration guides. 
     * Responses:
     *  - 200: **OK** - Details of the selected payout 
     *  - 401: 
     *  - 403: 
     *
     * @param id ID of the payout
     * @return [GetPayoutById200ResponseDto]
     */
    @GET("payouts/{id}")
    suspend fun getPayoutById(@Path("id") id: kotlin.String): Response<GetPayoutById200ResponseDto>

    /**
     * Payout list
     * **Payouts are only available when using Volt Connect**  This will return a list of all existing payouts.  ### Usage The list is paginated by default and can be filtered using the parameters shown in the request section below.  ### More information Full details on payouts can be found in our comprehensive integration guides. 
     * Responses:
     *  - 200: **OK** - A paginated list of payouts. 
     *  - 401: 
     *  - 403: 
     *
     * @param page You can request additional pages of information by appending the page parameter to your GET request. Pages start numbering at 1 and are in blocks of 50 records, so to call records 51-100, append **page**&#x3D;2 to your request. If you omit the page parameter, we will return you the information on page 1.  (optional, default to 1)
     * @param limit If you want to return more than the default number of records per page, append the parameter items to your GET request. To get the first 100 records, append **limit**&#x3D;100 to your request. To get the next 100 records, append **page**&#x3D;2&amp;**limit**&#x3D;100. If you omit the items parameter, we will return 50 records per page.  (optional, default to 50)
     * @return [kotlin.collections.List<PayoutItemDto>]
     */
    @GET("payouts")
    suspend fun getPayoutList(@Query("page") page: kotlin.Int? = 1, @Query("limit") limit: kotlin.Int? = 50): Response<kotlin.collections.List<PayoutItemDto>>

}
