/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.volt.client.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param id 
 * @param status 
 * @param createdAt 
 */


data class ReportRequestedDto (

    @get:JsonProperty("id")
    val id: java.util.UUID,

    @get:JsonProperty("status")
    val status: ReportRequestedDto.Status,

    @get:JsonProperty("createdAt")
    val createdAt: java.time.OffsetDateTime

) {

    /**
     * 
     *
     * Values: REQUESTED,GENERATING,AVAILABLE,FAILED,REMOVAL_REQUESTED
     */
    enum class Status(val value: kotlin.String) {
        @JsonProperty(value = "REPORT_REQUESTED") REQUESTED("REPORT_REQUESTED"),
        @JsonProperty(value = "REPORT_GENERATING") GENERATING("REPORT_GENERATING"),
        @JsonProperty(value = "REPORT_AVAILABLE") AVAILABLE("REPORT_AVAILABLE"),
        @JsonProperty(value = "REPORT_FAILED") FAILED("REPORT_FAILED"),
        @JsonProperty(value = "REPORT_REMOVAL_REQUESTED") REMOVAL_REQUESTED("REPORT_REMOVAL_REQUESTED");
    }

}

