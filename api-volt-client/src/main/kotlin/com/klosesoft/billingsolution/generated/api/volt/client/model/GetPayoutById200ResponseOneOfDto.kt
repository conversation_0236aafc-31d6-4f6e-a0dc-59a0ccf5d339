/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package com.klosesoft.billingsolution.generated.api.volt.client.model

import com.klosesoft.billingsolution.generated.api.volt.client.model.PayoutDetailsAllOfCreatedByDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PayoutItemBeneficiaryDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.PayoutItemSenderDto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param id 
 * @param status 
 * @param createdAt 
 * @param amount The amount to be paid out, in 1/100 units (pence, cents etc).
 * @param currency Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters)
 * @param paymentTitle Payment reference to be used for the payout.  This will appear on the beneficiary's statement.
 * @param beneficiary 
 * @param sender 
 * @param createdBy 
 * @param approvals Will contain an empty array
 */


data class GetPayoutById200ResponseOneOfDto (

    @get:JsonProperty("id")
    val id: java.util.UUID? = null,

    @get:JsonProperty("status")
    val status: GetPayoutById200ResponseOneOfDto.Status? = null,

    @get:JsonProperty("createdAt")
    val createdAt: java.time.OffsetDateTime? = null,

    /* The amount to be paid out, in 1/100 units (pence, cents etc). */
    @get:JsonProperty("amount")
    val amount: kotlin.Int? = null,

    /* Currency in which the payment should be made, in ISO 4217 format (3 uppercase letters) */
    @get:JsonProperty("currency")
    val currency: kotlin.String? = null,

    /* Payment reference to be used for the payout.  This will appear on the beneficiary's statement. */
    @get:JsonProperty("paymentTitle")
    val paymentTitle: kotlin.String? = null,

    @get:JsonProperty("beneficiary")
    val beneficiary: PayoutItemBeneficiaryDto? = null,

    @get:JsonProperty("sender")
    val sender: PayoutItemSenderDto? = null,

    @get:JsonProperty("createdBy")
    val createdBy: PayoutDetailsAllOfCreatedByDto? = null,

    /* Will contain an empty array */
    @get:JsonProperty("approvals")
    val approvals: kotlin.collections.List<kotlin.String>? = null

) {

    /**
     * 
     *
     * Values: CREATED
     */
    enum class Status(val value: kotlin.String) {
        @JsonProperty(value = "CREATED") CREATED("CREATED");
    }

}

