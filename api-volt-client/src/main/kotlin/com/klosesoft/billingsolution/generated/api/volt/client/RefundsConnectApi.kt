package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout403ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.CreatePayout422ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.GetRefundDetails200ResponseDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.RefundRequestDto
import com.klosesoft.billingsolution.generated.api.volt.client.model.RequestRefundByPaymentId201ResponseDto

interface RefundsConnectApi {
    /**
     * Check refund eligibility
     * **Refunds are only available when using Volt Connect**  ### Usage Pass the **id** of the existing payment in the URL to check whether this payment is eligible for a refund.  ### More information Full details on how to request refunds, with full details of notifications you can expect, can be found in our comprehensive integration guides. 
     * Responses:
     *  - 200: All payments details for specified filters
     *  - 400: 
     *  - 401: 
     *  - 403: 
     *
     * @param id ID of the refund
     * @return [GetRefundDetails200ResponseDto]
     */
    @GET("payments/{id}/refund-details")
    suspend fun getRefundDetails(@Path("id") id: java.util.UUID): Response<GetRefundDetails200ResponseDto>

    /**
     * New refund request
     * **Refunds are only available when using Volt Connect**  ### Usage Pass the **id** of the existing payment in the URL and the amount and reference for the refund in the body of the request.  ### Checking eligibility before requesting a refund You can get the eligibility for a refund using GET /payments/{id}/refund-details  ### More information Full details on how to use refunds, with details of notifications you can expect, can be found in our comprehensive integration guides. 
     * Responses:
     *  - 201: **Created** - The refund request was validated and accepted. 
     *  - 400: 
     *  - 401: 
     *  - 403: **Not authorised** - Although your credentials are correct, your access to this section of the API has been disabled or limited. Subsequent requests to this endpoint (even with valid data) will not be processed. 
     *  - 409: 
     *  - 422: **Unprocessable Entity** - The payout request could not be completed due to a problem with the beneficiary you're trying to send to.  If the exception message does not provide enough information, please contact merchant support for further details. 
     *
     * @param id ID of the payment
     * @param idempotencyKey Optional - but recommended - to avoid duplicate refund requests (optional)
     * @param xJWSHeader Optional - applicable if Volt is your Connect partner (optional)
     * @param refundRequestDto Details of the refund you&#39;d like to request for this payment (optional)
     * @return [RequestRefundByPaymentId201ResponseDto]
     */
    @POST("payments/{id}/request-refund")
    suspend fun requestRefundByPaymentId(@Path("id") id: kotlin.String, @Header("idempotency-key") idempotencyKey: java.util.UUID? = null, @Header("X-JWS-Header") xJWSHeader: kotlin.String? = null, @Body refundRequestDto: RefundRequestDto? = null): Response<RequestRefundByPaymentId201ResponseDto>

}
