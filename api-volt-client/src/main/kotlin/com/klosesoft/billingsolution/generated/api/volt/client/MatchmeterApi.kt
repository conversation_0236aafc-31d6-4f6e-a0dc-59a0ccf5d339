package com.klosesoft.billingsolution.generated.api.volt.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.volt.client.model.GetPaymentsPaymentIdNameMatchScore200ResponseDto

interface MatchmeterApi {
    /**
     * Payment Match Score
     * This endpoint will allow accurately matches naming nuances between merchant inputs and end-user bank records
     * Responses:
     *  - 200: OK
     *  - 403: **Not authorised** - Although your credentials are correct, your access to this section of the API has been disabled or limited. Subsequent requests to this endpoint (even with valid data) will not be processed.
     *  - 404: **Not found** - The resource that you are requesting cannot be found. The response may contain further information.
     *  - 409: **Conflict** - Name match score is only available for payments in the received status.
     *
     * @param paymentId 
     * @param allowInitials default: true - if set to false, receiving an account holder name that contains an initial instead of a first name will significantly impact the match score. (optional)
     * @return [GetPaymentsPaymentIdNameMatchScore200ResponseDto]
     */
    @GET("payments/{paymentId}/name-match-score")
    suspend fun getPaymentsPaymentIdNameMatchScore(@Path("paymentId") paymentId: kotlin.String, @Query("allow_initials") allowInitials: kotlin.Boolean? = null): Response<GetPaymentsPaymentIdNameMatchScore200ResponseDto>

}
