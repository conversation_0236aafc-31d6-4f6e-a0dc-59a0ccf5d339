/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalBusinessSegmentDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalBusinessSegmentsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalBusinessSegmentApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/businesssegments"],
            produces = ["application/json"],
            consumes = ["multipart/form-data"]
    )
    suspend fun createBusinessSegment( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String,@NotNull  @Valid @RequestParam(value = "invoiceNumberPattern", required = true) invoiceNumberPattern: kotlin.String,@NotNull  @Valid @RequestParam(value = "vatId", required = true) vatId: kotlin.String,@NotNull  @Valid @RequestParam(value = "originatorAddressKey", required = true) originatorAddressKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "version", required = true) version: kotlin.Long, @Valid @RequestPart("logo", required = true) logo: org.springframework.core.io.Resource): ResponseEntity<WebPortalBusinessSegmentDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/businesssegments/{segment_key}"],
            produces = ["application/json"]
    )
    suspend fun loadBusinessSegment( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("segment_key") segmentKey: kotlin.String): ResponseEntity<WebPortalBusinessSegmentDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/businesssegments"],
            produces = ["application/json"]
    )
    suspend fun loadBusinessSegments( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalBusinessSegmentsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/businesssegments/{segment_key}"],
            produces = ["application/json"],
            consumes = ["multipart/form-data"]
    )
    suspend fun updateBusinessSegment( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("segment_key") segmentKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "invoiceNumberPattern", required = true) invoiceNumberPattern: kotlin.String,@NotNull  @Valid @RequestParam(value = "vatId", required = true) vatId: kotlin.String,@NotNull  @Valid @RequestParam(value = "originatorAddressKey", required = true) originatorAddressKey: kotlin.String,@NotNull  @Valid @RequestParam(value = "version", required = true) version: kotlin.Long, @Valid @RequestPart("logo", required = false) logo: org.springframework.core.io.Resource?): ResponseEntity<WebPortalBusinessSegmentDetailsDto>
}
