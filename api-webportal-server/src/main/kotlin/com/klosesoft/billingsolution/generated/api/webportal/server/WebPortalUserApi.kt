/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCurrentUserResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalUserDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalUserProfileDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalUsersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalUserApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/users"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createUser( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalUserDetailsDto: WebPortalUserDetailsDto): ResponseEntity<WebPortalUserDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/user/current"],
            produces = ["application/json"]
    )
    suspend fun loadCurrentUser( @Valid @RequestParam(value = "x-tenant-key", required = false) xTenantKey: kotlin.String?): ResponseEntity<WebPortalCurrentUserResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/users/{key}"],
            produces = ["application/json"]
    )
    suspend fun loadUser( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String): ResponseEntity<WebPortalUserDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/user/profile"],
            produces = ["application/json"]
    )
    suspend fun loadUserProfile(@NotNull  @Valid @RequestParam(value = "x-tenant-key", required = true) xTenantKey: kotlin.String): ResponseEntity<WebPortalUserProfileDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/users"],
            produces = ["application/json"]
    )
    suspend fun loadUsers( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalUsersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/user/current/last-tenant"]
    )
    suspend fun saveLastTenant(@NotNull  @Valid @RequestParam(value = "last-used-tenant", required = true) lastUsedTenant: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/users/{key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateUser( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String, @Valid @RequestBody webPortalUserDetailsDto: WebPortalUserDetailsDto): ResponseEntity<WebPortalUserDetailsDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/user/profile"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateUserProfile(@NotNull  @Valid @RequestParam(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalUserProfileDto: WebPortalUserProfileDto): ResponseEntity<WebPortalUserProfileDto>
}
