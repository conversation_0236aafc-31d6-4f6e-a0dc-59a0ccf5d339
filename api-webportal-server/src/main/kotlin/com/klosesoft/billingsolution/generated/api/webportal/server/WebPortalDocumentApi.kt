/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.DocumentFormatDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalDocumentDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalDocumentItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalDocumentsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalKeysPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalDocumentApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/documents/{key}"],
            produces = ["application/json"]
    )
    suspend fun loadDocument( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String): ResponseEntity<WebPortalDocumentDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/documents/{document_key}"],
            produces = ["application/pdf"]
    )
    suspend fun loadDocumentFile( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("document_key") documentKey: kotlin.String, @Valid @RequestParam(value = "format", required = false) format: DocumentFormatDto?, @Valid @RequestParam(value = "pdfCopy", required = false, defaultValue = "false") pdfCopy: kotlin.Boolean): ResponseEntity<kotlin.ByteArray>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/documents/{document_key}/items"],
            produces = ["application/json"]
    )
    suspend fun loadDocumentItems( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("document_key") documentKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalDocumentItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/documents/keys"],
            produces = ["application/json"]
    )
    suspend fun loadDocumentKeys( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalKeysPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/documents"],
            produces = ["application/json"]
    )
    suspend fun loadDocuments( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalDocumentsPagedResponseDto>
}
