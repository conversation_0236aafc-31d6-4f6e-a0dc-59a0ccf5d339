/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSettlementReportDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSettlementReportItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSettlementReportsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalSettlementReportApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/settlement-reports/{key}"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReport( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String): ResponseEntity<WebPortalSettlementReportDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/settlement-reports/{key}/settlement-report-items"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReportItems( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalSettlementReportItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/settlement-reports"],
            produces = ["application/json"]
    )
    suspend fun loadSettlementReports( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalSettlementReportsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/settlement-report-files"],
            consumes = ["multipart/form-data"]
    )
    suspend fun uploadSettlementReportFile( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestPart("report", required = true) report: org.springframework.core.io.Resource): ResponseEntity<Unit>
}
