/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.AddressDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCustomerDetailsCustomerAddressStatusUpdateRequestDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCustomerDetailsDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCustomerDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCustomerShippingAddressesPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalCustomersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalCustomerApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/customers/{customer_key}/shippingaddresses"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun addCustomerShippingAddress( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/customers"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createCustomer( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalCustomerDetailsDataDto: WebPortalCustomerDetailsDataDto): ResponseEntity<WebPortalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/customers/{customer_key}"],
            produces = ["application/json"]
    )
    suspend fun loadCustomer( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String): ResponseEntity<WebPortalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/customers/{customer_key}/shippingaddresses"],
            produces = ["application/json"]
    )
    suspend fun loadCustomerShippingAddresses( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalCustomerShippingAddressesPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/customers"],
            produces = ["application/json"]
    )
    suspend fun loadCustomers( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalCustomersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PATCH],
            value = ["/webportal/v1/customers/{customer_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomer( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody webPortalCustomerDetailsDataDto: WebPortalCustomerDetailsDataDto): ResponseEntity<WebPortalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/customers/{customer_key}/shippingaddresses/{address_key}/status"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerAddressStatus( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String, @Valid @RequestBody webPortalCustomerDetailsCustomerAddressStatusUpdateRequestDto: WebPortalCustomerDetailsCustomerAddressStatusUpdateRequestDto): ResponseEntity<WebPortalCustomerDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/customers/{customer_key}/billingaddress"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerBillingAddress( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/customers/{customer_key}/shippingaddresses/{address_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateCustomerShippingAddress( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("customer_key") customerKey: kotlin.String, @PathVariable("address_key") addressKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>
}
