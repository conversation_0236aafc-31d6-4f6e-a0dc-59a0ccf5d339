/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalNotificationReceiverDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalNotificationReceiversPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalNotificationsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalNotificationApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/notification-receivers"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createNotificationReceiver( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalNotificationReceiverDetailsDto: WebPortalNotificationReceiverDetailsDto): ResponseEntity<WebPortalNotificationReceiverDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/notification-receivers/{key}"],
            produces = ["application/json"]
    )
    suspend fun loadNotificationReceiver( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String): ResponseEntity<WebPortalNotificationReceiverDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/notification-receivers"],
            produces = ["application/json"]
    )
    suspend fun loadNotificationReceivers( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalNotificationReceiversPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/notifications"],
            produces = ["application/json"]
    )
    suspend fun loadNotifications( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalNotificationsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/notifications/{key}/resend"]
    )
    suspend fun resendNotification( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/notification-receivers/{key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateNotificationReceiver( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("key") key: kotlin.String, @Valid @RequestBody webPortalNotificationReceiverDetailsDto: WebPortalNotificationReceiverDetailsDto): ResponseEntity<WebPortalNotificationReceiverDetailsDto>
}
