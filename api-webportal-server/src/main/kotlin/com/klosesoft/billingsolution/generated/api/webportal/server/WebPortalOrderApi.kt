/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.CreateNoteRequestDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.IsCancelable200ResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.IsFinalizable200ResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.IsUpdatable200ResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.NoteResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.UpdateNoteRequestDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalDocumentsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalKeysPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderBalanceCaseItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderCaptureDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderHistoryEntryPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderNotePagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderPostingRecordsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderStatusResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderTemplateItemsPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderTemplateRequestDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderTemplatesPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrderUpdateDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalOrdersPagedResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSuppliersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalOrderApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/orders/{order_key}/notes"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun addOrderNote( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestBody createNoteRequestDto: CreateNoteRequestDto): ResponseEntity<NoteResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/orders/{order_key}/cancel"],
            produces = ["application/json"]
    )
    suspend fun cancelOrder( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<WebPortalOrderStatusResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/orders/keys"],
            consumes = ["application/json"]
    )
    suspend fun createOrder( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalOrderCaptureDto: WebPortalOrderCaptureDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/businesssegments/{business_segment_key}/ordertemplates"],
            consumes = ["application/json"]
    )
    suspend fun createOrderTemplate( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("business_segment_key") businessSegmentKey: kotlin.String, @Valid @RequestBody webPortalOrderTemplateRequestDto: WebPortalOrderTemplateRequestDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.DELETE],
            value = ["/webportal/v1/orders/{order_key}/notes/{note_key}"]
    )
    suspend fun deleteOrderNote( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("note_key") noteKey: kotlin.String): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/orders/{order_key}/finalize"],
            produces = ["application/json"]
    )
    suspend fun finalizeOrder( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<WebPortalOrderStatusResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/isCancelable"],
            produces = ["application/json"]
    )
    suspend fun isCancelable( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<IsCancelable200ResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/isFinalizable"],
            produces = ["application/json"]
    )
    suspend fun isFinalizable( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<IsFinalizable200ResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/isUpdatable"],
            produces = ["application/json"]
    )
    suspend fun isUpdatable( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<IsUpdatable200ResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}"],
            produces = ["application/json"]
    )
    suspend fun loadOrder( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<WebPortalOrderDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/balanceCaseItems"],
            produces = ["application/json"]
    )
    suspend fun loadOrderBalanceCaseItems( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderBalanceCaseItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/documents"],
            produces = ["application/json"]
    )
    suspend fun loadOrderDocuments( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?): ResponseEntity<WebPortalDocumentsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/historyentries"],
            produces = ["application/json"]
    )
    suspend fun loadOrderHistoryEntries( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderHistoryEntryPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/items"],
            produces = ["application/json"]
    )
    suspend fun loadOrderItems( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/keys"],
            produces = ["application/json"]
    )
    suspend fun loadOrderKeys( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalKeysPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/notes"],
            produces = ["application/json"]
    )
    suspend fun loadOrderNotes( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderNotePagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/postingrecords"],
            produces = ["application/json"]
    )
    suspend fun loadOrderPostingRecords( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderPostingRecordsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/suppliers"],
            produces = ["application/json"]
    )
    suspend fun loadOrderSuppliers( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalSuppliersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/businesssegments/{business_segment_key}/ordertemplates/{order_template_key}/items"],
            produces = ["application/json"]
    )
    suspend fun loadOrderTemplateItems( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("business_segment_key") businessSegmentKey: kotlin.String, @PathVariable("order_template_key") orderTemplateKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderTemplateItemsPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/businesssegments/{business_segment_key}/ordertemplates"],
            produces = ["application/json"]
    )
    suspend fun loadOrderTemplates( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("business_segment_key") businessSegmentKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrderTemplatesPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders/{order_key}/update"],
            produces = ["application/json"]
    )
    suspend fun loadOrderUpdate( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String): ResponseEntity<WebPortalOrderDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/orders"],
            produces = ["application/json"]
    )
    suspend fun loadOrders( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalOrdersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/orders/{order_key}/update"],
            consumes = ["application/json"]
    )
    suspend fun updateOrder( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @Valid @RequestBody webPortalOrderUpdateDetailsDto: WebPortalOrderUpdateDetailsDto): ResponseEntity<Unit>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/orders/{order_key}/notes/{note_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateOrderNote( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("order_key") orderKey: kotlin.String, @PathVariable("note_key") noteKey: kotlin.String, @Valid @RequestBody updateNoteRequestDto: UpdateNoteRequestDto): ResponseEntity<NoteResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/businesssegments/{business_segment_key}/ordertemplates/{key}"],
            consumes = ["application/json"]
    )
    suspend fun updateOrderTemplate( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("business_segment_key") businessSegmentKey: kotlin.String, @PathVariable("key") key: kotlin.String, @Valid @RequestBody webPortalOrderTemplateRequestDto: WebPortalOrderTemplateRequestDto): ResponseEntity<Unit>
}
