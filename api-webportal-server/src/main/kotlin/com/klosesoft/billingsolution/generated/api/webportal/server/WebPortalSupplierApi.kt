/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.AddressDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSupplierDetailsDataDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSupplierDetailsResponseDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalSuppliersPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalSupplierApi {


    @RequestMapping(
            method = [RequestMethod.POST],
            value = ["/webportal/v1/suppliers"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun createSupplier( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestBody webPortalSupplierDetailsDataDto: WebPortalSupplierDetailsDataDto): ResponseEntity<WebPortalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/suppliers/{supplier_key}"],
            produces = ["application/json"]
    )
    suspend fun loadSupplier( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("supplier_key") supplierKey: kotlin.String): ResponseEntity<WebPortalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/suppliers"],
            produces = ["application/json"]
    )
    suspend fun loadSuppliers( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalSuppliersPagedResponseDto>


    @RequestMapping(
            method = [RequestMethod.PATCH],
            value = ["/webportal/v1/suppliers/{supplier_key}"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateSupplier( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("supplier_key") supplierKey: kotlin.String, @Valid @RequestBody webPortalSupplierDetailsDataDto: WebPortalSupplierDetailsDataDto): ResponseEntity<WebPortalSupplierDetailsResponseDto>


    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/webportal/v1/suppliers/{supplier_key}/billingaddress"],
            produces = ["application/json"],
            consumes = ["application/json"]
    )
    suspend fun updateSupplierBillingAddress( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("supplier_key") supplierKey: kotlin.String, @Valid @RequestBody addressDto: AddressDto): ResponseEntity<AddressDto>
}
