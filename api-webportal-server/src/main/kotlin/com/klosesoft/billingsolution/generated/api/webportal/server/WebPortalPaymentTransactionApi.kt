/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.8.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package com.klosesoft.billingsolution.generated.api.webportal.server

import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalPaymentTransactionDetailsDto
import com.klosesoft.billingsolution.generated.api.webportal.server.model.WebPortalPaymentTransactionsPagedResponseDto
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlinx.coroutines.flow.Flow
import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface WebPortalPaymentTransactionApi {


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/paymenttransactions/count-open"],
            produces = ["application/json"]
    )
    suspend fun countOpenPaymentTransactions( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String): ResponseEntity<kotlin.Long>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/paymenttransactions/{payment_transaction_key}"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentTransactionClearing( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @PathVariable("payment_transaction_key") paymentTransactionKey: kotlin.String): ResponseEntity<WebPortalPaymentTransactionDetailsDto>


    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/webportal/v1/paymenttransactions"],
            produces = ["application/json"]
    )
    suspend fun loadPaymentTransactions( @RequestHeader(value = "x-tenant-key", required = true) xTenantKey: kotlin.String, @Valid @RequestParam(value = "page", required = false) page: kotlin.Int?, @Valid @RequestParam(value = "size", required = false) size: kotlin.Int?, @Valid @RequestParam(value = "sort", required = false) sort: kotlin.String?, @Valid @RequestParam(value = "direction", required = false) direction: kotlin.String?, @Valid @RequestParam(value = "filter", required = false) filter: kotlin.String?): ResponseEntity<WebPortalPaymentTransactionsPagedResponseDto>
}
