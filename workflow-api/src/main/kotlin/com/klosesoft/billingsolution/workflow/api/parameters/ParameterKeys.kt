package com.klosesoft.billingsolution.workflow.api.parameters

object ParameterKeys {
    // just for the start
    const val TENANT_KEY = "tenantKey"
    const val START_USER_KEY = "startUserKey"
    const val ORDER_DOMAIN_DTO = "orderDomainDto"
    const val SUBSCRIPTION_DOMAIN_DTO = "subscriptionDomainDto"

    // global parameters
    const val GLOBAL_TENANT_ID = "tenantId"
    const val GLOBAL_ORDER_ID = "orderId"
    const val GLOBAL_SUBSCRIPTION_ID = "subscriptionId"

    // specific parameters

    // payment assignment
    const val PAYMENT_ASSIGNMENT_PREFIX = "paymentAssignment_"

    const val PAYMENT_ASSIGNMENT_ID = "${PAYMENT_ASSIGNMENT_PREFIX}paymentAssignmentId"

    const val PAYMENT_ASSIGNMENT_PAYMENT_TRANSACTION_ID = "${PAYMENT_ASSIGNMENT_PREFIX}paymentTransactionId"
    const val PAYMENT_ASSIGNMENT_DTO = "${PAYMENT_ASSIGNMENT_PREFIX}paymentAssignmentDto"
    const val PAYMENT_ASSIGNMENT_REVERSING_KEY = "${PAYMENT_ASSIGNMENT_PREFIX}reversePaymentAssignmentKey"
    const val PAYMENT_ASSIGNMENT_OPERATION = "${PAYMENT_ASSIGNMENT_PREFIX}paymentAssignmentOperation"
    const val PAYMENT_ASSIGNMENT_USER = "${PAYMENT_ASSIGNMENT_PREFIX}user"

    // approval
    private const val APPROVAL_PREFIX = "approval_"
    const val APPROVAL_APPROVED = "${APPROVAL_PREFIX}approved"

    // update
    const val UPDATE_PREFIX = "update_"
    const val UPDATE_ORDER_ACTION = "${UPDATE_PREFIX}orderAction"
    const val UPDATE_ORDER_USER_ID = "${UPDATE_PREFIX}orderUserId"
    const val UPDATE_ORDER_ITEM_DOMAIN_DTO = "${UPDATE_PREFIX}orderItemDomainDto"
    const val UPDATE_ORDER_ITEM_KEY = "${UPDATE_PREFIX}orderItemKey"
    const val UPDATE_ORDER_DOMAIN_DTO = "${UPDATE_PREFIX}orderDomainDto"

    // finalization
    private const val FINALIZATION_PREFIX = "finalization_"
    const val FINALIZATION_USER_ID = "${FINALIZATION_PREFIX}userId"
    const val FINALIZATION_ORDER_ALREADY_FINALIZED = "${FINALIZATION_PREFIX}orderAlreadyFinalized"

    // cancellation
    private const val CANCELLATION_PREFIX = "cancellation_"
    const val CANCELLATION_USER_ID = "${CANCELLATION_PREFIX}userId"
    const val CANCELLATION_REASON = "${CANCELLATION_PREFIX}reason"

    // subscription-specific parameters
    private const val SUBSCRIPTION_PREFIX = "subscription_"
    const val SUBSCRIPTION_USER_ID = "${SUBSCRIPTION_PREFIX}userId"
    const val SUBSCRIPTION_ACTION = "${SUBSCRIPTION_PREFIX}action"
    const val SUBSCRIPTION_BILLING_PERIOD = "${SUBSCRIPTION_PREFIX}billingPeriod"
    const val SUBSCRIPTION_ALREADY_PROCESSED = "${SUBSCRIPTION_PREFIX}alreadyProcessed"
}
