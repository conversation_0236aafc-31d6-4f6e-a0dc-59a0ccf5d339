package com.klosesoft.billingsolution.workflow.api.parameters

import java.util.UUID

data class SubscriptionWorkflowParameters(
    val tenantId: UUID,
    val subscriptionId: UUID,
) {

    companion object {
        fun fromMap(
            map: Map<String, Any>,
        ): SubscriptionWorkflowParameters {
            return SubscriptionWorkflowParameters(
                tenantId = toUUID(map[ParameterKeys.GLOBAL_TENANT_ID] as String),
                subscriptionId = toUUID(map[ParameterKeys.GLOBAL_SUBSCRIPTION_ID] as String),
            )
        }

        private fun toUUID(
            it: String,
        ) = UUID.fromString(it)
    }
}
