package com.klosesoft.billingsolution.workflow.api.parameters

import com.fasterxml.jackson.databind.ObjectMapper
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto

data class StartSubscriptionWorkflowParameters(
    val tenantKey: String,
    val userKey: String,
    val subscriptionDomainDto: SubscriptionDomainDto,
) {
    fun toMap(
        objectMapper: ObjectMapper,
    ): Map<String, Any> = mapOf(
        ParameterKeys.TENANT_KEY to tenantKey,
        ParameterKeys.START_USER_KEY to userKey,
        ParameterKeys.SUBSCRIPTION_DOMAIN_DTO to objectMapper.writeValueAsString(subscriptionDomainDto),
    )

    companion object {
        fun fromMap(
            map: Map<String, Any>,
            objectMapper: ObjectMapper? = null,
        ): StartSubscriptionWorkflowParameters {
            val subscriptionDomainDto = objectMapper?.readValue(
                map[ParameterKeys.SUBSCRIPTION_DOMAIN_DTO] as String,
                SubscriptionDomainDto::class.java,
            )
            return StartSubscriptionWorkflowParameters(
                tenantKey = map[ParameterKeys.TENANT_KEY] as String,
                userKey = map[ParameterKeys.START_USER_KEY] as String,
                subscriptionDomainDto = subscriptionDomainDto!!,
            )
        }
    }
}
