#!/bin/bash

# Subscription Workflow Demo Script
# This script demonstrates the subscription workflow functionality

echo "🚗 Velocity Wheels Subscription Workflow Demo"
echo "=============================================="
echo ""

# Check if the application is running
echo "📋 Checking if the application is running..."
if ! curl -s http://localhost:8080/actuator/health > /dev/null; then
    echo "❌ Application is not running. Please start the billing-solution application first."
    echo "   Run: ./gradlew bootRun"
    exit 1
fi

echo "✅ Application is running"
echo ""

# Run the Velocity Wheels subscription demo
echo "🎯 Starting Velocity Wheels Subscription Workflow Demo..."
echo ""

response=$(curl -s -w "%{http_code}" http://localhost:8080/api/demo/subscription/velocity-wheels)
http_code="${response: -3}"
response_body="${response%???}"

if [ "$http_code" = "200" ]; then
    echo "✅ Demo completed successfully!"
    echo ""
    echo "📊 Response:"
    echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    echo ""
    echo "🔍 What happened:"
    echo "  1. Created a new BMW X5 lease subscription"
    echo "  2. Started the Velocity Wheels subscription workflow"
    echo "  3. Processed through workflow steps:"
    echo "     - Subscription creation"
    echo "     - Subscription activation"
    echo "     - Billing processing"
    echo "     - Invoice creation"
    echo "     - Invoice sending"
    echo ""
    echo "📈 The subscription is now active and ready for billing cycles!"
    echo ""
    echo "🎉 Demo completed successfully!"
else
    echo "❌ Demo failed with HTTP code: $http_code"
    echo "📋 Response:"
    echo "$response_body"
fi

echo ""
echo "📚 For more information, see SUBSCRIPTION_WORKFLOW_README.md"
echo ""
echo "🔧 Additional commands you can try:"
echo "  - Check workflow instances: curl http://localhost:8080/api/workflow/instances"
echo "  - View subscription status: curl http://localhost:8080/api/subscriptions"
echo "  - Monitor processing status via the web portal"
echo ""
