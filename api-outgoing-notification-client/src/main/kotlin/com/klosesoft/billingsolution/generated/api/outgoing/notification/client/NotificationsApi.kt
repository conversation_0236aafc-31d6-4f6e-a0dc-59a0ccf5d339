package com.klosesoft.billingsolution.generated.api.outgoing.notification.client

import org.openapitools.client.infrastructure.CollectionFormats.*
import retrofit2.http.*
import retrofit2.Response
import okhttp3.RequestBody
import com.fasterxml.jackson.annotation.JsonProperty

import com.klosesoft.billingsolution.generated.api.outgoing.notification.client.model.NotificationDto

interface NotificationsApi {
    /**
     * Receive a new notification
     * 
     * Responses:
     *  - 200: The notification was successfully received
     *  - 400: Invalid input
     *  - 404: Target object not found
     *  - 500: Internal server error
     *
     * @param notificationDto 
     * @return [Unit]
     */
    @POST("v1/notifications")
    suspend fun receiveNotification(@Body notificationDto: NotificationDto): Response<Unit>

}
