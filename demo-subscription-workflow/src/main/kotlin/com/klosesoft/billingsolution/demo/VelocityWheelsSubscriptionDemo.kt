package com.klosesoft.billingsolution.demo

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.api.service.load.SubscriptionHistoryEntryLoadDomainApiService
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.delay
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate

@Service
class VelocityWheelsSubscriptionDemo(
    private val workflowApiService: WorkflowApiService,
    private val subscriptionHistoryEntryLoadDomainApiService: SubscriptionHistoryEntryLoadDomainApiService,
) {
    private val logger = KotlinLogging.logger {}

    suspend fun runDemo() {
        logger.info { "Starting Velocity Wheels Subscription Workflow Demo" }

        try {
            // Create a demo subscription for car leasing
            val subscriptionDto = createDemoSubscription()

            logger.info { "Creating subscription: ${subscriptionDto.key}" }

            // Start the subscription workflow
            workflowApiService.startSubscriptionWorkflow(
                tenantKey = BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS,
                userKey = BillingSolutionConstants.SYSTEM_USER_KEY,
                subscriptionDomainDto = subscriptionDto,
            )

            logger.info { "Subscription workflow started successfully for ${subscriptionDto.key}" }

            // Wait a bit for the workflow to process
            delay(3000)

            // Check subscription history
            logger.info { "Checking subscription history..." }
            try {
                val historyPage = subscriptionHistoryEntryLoadDomainApiService.findAllByTenantKey(
                    tenantKey = BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS,
                    subscriptionKey = subscriptionDto.key,
                    rsqlFilter = null,
                    pageable = PageRequest.of(0, 10),
                )

                logger.info { "Found ${historyPage.totalElements} history entries for subscription ${subscriptionDto.key}" }
                historyPage.content.forEach { entry ->
                    logger.info { "  ${entry.number}: ${entry.processingStatus} at ${entry.createdAt} by ${entry.createdBy}" }
                    if (entry.additionalInformation != null) {
                        logger.info { "    Additional info: ${entry.additionalInformation}" }
                    }
                }
            } catch (e: Exception) {
                logger.warn(e) { "Could not retrieve subscription history (this is expected if the subscription was just created)" }
            }

            logger.info { "Demo completed successfully!" }

        } catch (e: Exception) {
            logger.error(e) { "Error running subscription workflow demo" }
            throw e
        }
    }

    private fun createDemoSubscription(): SubscriptionDomainDto {
        return SubscriptionDomainDto(
            key = "VW-SUB-${System.currentTimeMillis()}",
            name = "BMW X5 Lease Subscription",
            description = "Monthly lease subscription for BMW X5 - Premium car leasing service",
            customerKey = "CUST-DEMO-001", // This should exist in the demo data
            status = SubscriptionStatus.ACTIVE,
            frequency = SubscriptionFrequency.MONTHLY,
            amount = BigDecimal("599.99"), // Monthly lease amount in GBP
            currency = Currency.GBP,
            startDate = LocalDate.now(),
            endDate = LocalDate.now().plusYears(3), // 3-year lease
            nextBillingDate = LocalDate.now().plusMonths(1),
            version = 0,
        )
    }
}
