package com.klosesoft.billingsolution.acceptancetest.helper

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.generated.api.external.client.model.CurrencyDto
import java.math.BigDecimal
import java.util.UUID

object HelperUtil {

    fun generateOrderKey(
        tenantKey: String,
    ): String = "${tenantKey}_${randomStringGenerator(12)}"

    fun fetchTenantCurrencyString(
        tenantKey: String,
    ) = fetchTenantCurrency(tenantKey).name

    fun fetchTenantCurrency(
        tenantKey: String,
    ) = when (tenantKey) {
        BillingSolutionConstants.TENANT_KEY_BIKESALE_UK -> CurrencyDto.GBP
        BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS -> CurrencyDto.GBP
        else -> CurrencyDto.EUR
    }

    private fun randomStringGenerator(
        length: Int,
    ): String {
        val charset = "0123456789ABCDEF"
        return (1..length)
            .map { charset.random() }
            .joinToString("")
    }

    fun generateRandomUUID(): String = UUID.randomUUID().toString()

    fun fetchTaxRate(
        tenantKey: String,
        taxCode: String? = null,
    ) = when (tenantKey) {
        BillingSolutionConstants.TENANT_KEY_BIKESALE_UK ->
            when (taxCode) {
                "A9" -> BigDecimal("0.00")
                else -> BigDecimal("20.00")
            }

        else -> BigDecimal("19.00")
    }
}
