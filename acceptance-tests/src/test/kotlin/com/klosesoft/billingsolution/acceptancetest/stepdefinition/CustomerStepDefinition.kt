package com.klosesoft.billingsolution.acceptancetest.stepdefinition

import com.klosesoft.billingsolution.acceptancetest.client.ClientFetcher
import com.klosesoft.billingsolution.acceptancetest.creator.CustomerCreator
import com.klosesoft.billingsolution.acceptancetest.helper.CustomAssertions
import com.klosesoft.billingsolution.acceptancetest.helper.HelperUtil
import com.klosesoft.billingsolution.acceptancetest.stepdefinition.context.CustomerContext
import com.klosesoft.billingsolution.acceptancetest.stepdefinition.context.TenantContext
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.generated.api.external.client.model.CustomerTypeDto
import com.klosesoft.billingsolution.generated.api.external.client.model.LanguageDto
import io.cucumber.java8.En
import io.cucumber.java8.Scenario
import kotlinx.coroutines.runBlocking
import org.springframework.http.HttpStatus

class CustomerStepDefinition(
    private val tenantContext: TenantContext,
    private val customerContext: CustomerContext,
    private val clientFetcher: ClientFetcher,
) : En {
    private lateinit var scenario: Scenario

    init {
        Before(1) { scenario: Scenario ->
            this.scenario = scenario
        }

        Given(
            "a business customer for tenant {word} {string} from country {string} with language {string}",
        ) { tenant: String, businessSegmentKey: String, country: String, language: String ->
            tenantContext.tenantKey = getTenantForName(tenant)
            tenantContext.businessSegmentKey = businessSegmentKey

            customerContext.language = LanguageDto.valueOf(language)
            customerContext.country = country
            customerContext.customerType = CustomerTypeDto.COMPANY
            customerContext.billingAddressKey = HelperUtil.generateRandomUUID()
        }

        Given(
            "a private customer for tenant {word} {string} from country {string} with language {string}",
        ) { tenant: String, businessSegmentKey: String, country: String, language: String ->
            tenantContext.tenantKey = getTenantForName(tenant)
            tenantContext.businessSegmentKey = businessSegmentKey

            customerContext.language = LanguageDto.valueOf(language)
            customerContext.country = country
            customerContext.customerType = CustomerTypeDto.PERSON
            customerContext.billingAddressKey = HelperUtil.generateRandomUUID()
        }

        Given(
            "a company customer for tenant {word} {string} from country {string} with language {string}",
        ) { tenant: String, businessSegmentKey: String, country: String, language: String ->
            tenantContext.tenantKey = getTenantForName(tenant)
            tenantContext.businessSegmentKey = businessSegmentKey

            customerContext.language = LanguageDto.valueOf(language)
            customerContext.country = country
            customerContext.customerType = CustomerTypeDto.COMPANY
            customerContext.billingAddressKey = HelperUtil.generateRandomUUID()
        }

        Given("the customer has a shipping address") {
            customerContext.shippingAddressKey = HelperUtil.generateRandomUUID()
        }

        When("creating this customer") {
            runBlocking {
                val customerResponse = clientFetcher.getFrontendFacadeApiClient(tenantContext.tenantKey).createCustomer(createCustomer())
                CustomAssertions.assertStatusCodeIs(HttpStatus.CREATED.value(), customerResponse.code())
            }
        }
    }

    private fun createCustomer() = when (customerContext.customerType) {
        CustomerTypeDto.PERSON -> {
            val firstname = "Julius"
            val lastname = "Caesar${kotlin.random.Random.nextInt(9)}"
            customerContext.name = "$firstname $lastname"

            CustomerCreator.createPerson(
                key = customerContext.key,
                firstName = firstname,
                lastName = lastname,
                country = "GB",
                billingAddressKey = customerContext.billingAddressKey!!,
                shippingAddressKey = customerContext.shippingAddressKey,
                language = customerContext.language,
            )
        }

        CustomerTypeDto.COMPANY -> {
            val companyName = "TestCompany${kotlin.random.Random.nextInt(9)}"
            customerContext.name = companyName

            CustomerCreator.createCompany(
                key = customerContext.key,
                companyName = companyName,
                vatId = "123456-7890",
                country = customerContext.country,
                billingAddressKey = customerContext.billingAddressKey!!,
                shippingAddressKey = customerContext.shippingAddressKey,
                properties = customerContext.properties,
                language = customerContext.language,
            )
        }
    }

    private fun getTenantForName(
        tenant: String,
    ) = when (tenant) {
        "BikeSaleUK" -> BillingSolutionConstants.TENANT_KEY_BIKESALE_UK
        "MvzGesund" -> BillingSolutionConstants.TENANT_KEY_MVZ_GESUND
        "Sunpower" -> BillingSolutionConstants.TENANT_KEY_SUNPOWER
        "VelocityWheels" -> BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS
        else -> throw IllegalArgumentException("Unknown tenant: $tenant")
    }
}
