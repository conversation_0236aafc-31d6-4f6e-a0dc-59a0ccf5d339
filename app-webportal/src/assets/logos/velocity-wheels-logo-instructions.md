# Velocity Wheels Logo Instructions

## Logo Design Specifications

The Velocity Wheels logo should be created as a PNG file with the following specifications:

### Colors (from invoice template):
- Primary Dark Blue: `#1E3A8A`
- Secondary Blue: `#3B82F6`
- Light Gray: `#E5E7EB`
- Background: `#F8FAFC`
- Text Gray: `#94A3B8`

### Design Elements:
1. **Wheel Icon**: A stylized car wheel with spokes, using gradient from secondary blue to primary dark blue
2. **Company Name**: "VELOCITY WHEELS" in bold, modern font
3. **Tagline**: "Car Leasing Solutions" in smaller text
4. **Motion Lines**: Speed/motion lines to convey movement and velocity

### Dimensions:
- Width: 200px
- Height: 80px
- Format: PNG with transparent background

### Usage:
The logo file should be saved as `logo-velocity-wheels.png` in the `app-webportal/src/assets/logos/` directory.

### SVG Source:
The SVG version is available as `logo-velocity-wheels.svg` and can be converted to PNG using online tools like:
- svgtopng.com
- freeconvert.com/svg-to-png
- picflow.com/convert/svg-to-png

### Implementation:
Once the PNG is created, it will be automatically loaded by the application when the theme is set to "velocity-wheels" via the app component logic:
```typescript
this.logoPath = 'assets/logos/logo-' + currentUser.userData.theme + '.png'
```
