@use '@angular/material' as mat;
@use '../app/_app.component-theme.scss' as app;

@include mat.core();

$velocity-wheels-brand: (
  50: #F8FAFC,   // Very light blue-gray (from invoice background)
  100: #F1F5F9,  // Light blue-gray
  200: #E2E8F0,  // Light blue-gray
  300: #CBD5E1,  // Medium light blue-gray
  400: #94A3B8,  // Medium blue-gray
  500: #3B82F6,  // Medium blue (from invoice secondary color)
  600: #2563EB,  // Darker medium blue
  700: #1D4ED8,  // Dark blue
  800: #1E3A8A,  // Dark blue (from invoice primary color)
  900: #1E40AF,  // Very dark blue
  A100: #DBEAFE, // Light blue accent
  A200: #93C5FD, // Medium blue accent
  A400: #3B82F6, // Blue accent (same as 500)
  A700: #1D4ED8, // Dark blue accent
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
    A100: rgba(black, 0.87),
    A200: rgba(black, 0.87),
    A400: white,
    A700: white,
  )
);

$primary: mat.m2-define-palette($velocity-wheels-brand, 800); // Using the dark blue as primary
$accent: mat.m2-define-palette($velocity-wheels-brand, 500);  // Using the medium blue as accent

$theme: mat.m2-define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
  ),
  typography: mat.m2-define-typography-config(),
  density: -3,
));

$primary-default: mat.get-theme-color($theme, primary, default);

@include mat.all-component-themes($theme);

@include mat.button-density(0);
@include app.theme($theme);

// Custom Velocity Wheels specific styles
.velocity-wheels-theme {
  // Header and navigation styling to match invoice design
  .mat-toolbar {
    background-color: #1E3A8A !important;
    color: white !important;
  }
  
  // Card styling to match invoice boxes
  .mat-card {
    border: 1px solid #E5E7EB;
    background-color: #F8FAFC;
  }
  
  // Button styling to match invoice design
  .mat-raised-button.mat-primary {
    background-color: #1E3A8A;
    color: white;
  }
  
  .mat-raised-button.mat-accent {
    background-color: #3B82F6;
    color: white;
  }
  
  // Table styling to match invoice table design
  .mat-table {
    background-color: white;
    
    .mat-header-row {
      background-color: #1E3A8A;
      color: white;
    }
    
    .mat-row:nth-child(even) {
      background-color: #F8FAFC;
    }
  }
  
  // Form field styling
  .mat-form-field {
    .mat-form-field-outline {
      color: #E5E7EB;
    }
    
    &.mat-focused .mat-form-field-outline-thick {
      color: #3B82F6;
    }
  }
  
  // Sidebar styling
  .mat-sidenav {
    background-color: #1E3A8A !important;
    
    .mat-list-item {
      color: white;
      
      &:hover {
        background-color: #3B82F6;
      }
    }
  }
}
