/* eslint-disable @typescript-eslint/no-extraneous-class */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import { APP_INITIALIZER, importProvidersFrom, LOCALE_ID, NgModule } from '@angular/core'
import { BrowserModule } from '@angular/platform-browser'
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { AppRoutingModule } from './app-routing.module'
import { AppComponent } from './app.component'
import { DecimalPipe, NgOptimizedImage, registerLocaleData } from '@angular/common'

import { BrowserAnimationsModule, provideAnimations } from '@angular/platform-browser/animations'
import { VERSION as CDK_VERSION } from '@angular/cdk'
import {
  MAT_DATE_LOCALE,
  MATERIAL_SANITY_CHECKS,
  MatNativeDateModule,
  VERSION as MAT_VERSION,
} from '@angular/material/core'

import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { MatCardModule } from '@angular/material/card'
import { MatMenuModule } from '@angular/material/menu'
import { MatToolbarModule } from '@angular/material/toolbar'
import { MatDividerModule } from '@angular/material/divider'
import { MatListModule } from '@angular/material/list'
import { MAT_FORM_FIELD_DEFAULT_OPTIONS, MatFormFieldModule } from '@angular/material/form-field'
import { MatInputModule } from '@angular/material/input'
import { MatDatepickerModule } from '@angular/material/datepicker'
import { MatGridListModule } from '@angular/material/grid-list'
import { MatTableModule } from '@angular/material/table'
import { MatSelectModule } from '@angular/material/select'
import { MatDialogModule } from '@angular/material/dialog'
import { MatSnackBarModule } from '@angular/material/snack-bar'
import { MatSidenavModule } from '@angular/material/sidenav'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatSortModule } from '@angular/material/sort'

import { DashboardComponent } from './features/dashboard/components/dashboard/dashboard.component'
import { UserSettingsComponent } from './features/administration/components/user-settings/user-settings.component'
import { OrderListComponent } from './features/billing/components/order-list/order-list.component'
import { OrderDetailsComponent } from './features/billing/components/order-details/order-details.component'
import { ConfirmDialogComponent } from './shared/components/confirm-dialog/confirm-dialog.component'
import { PdfViewerDialogComponent } from './shared/components/pdf-viewer-dialog/pdf-viewer-dialog.component'
import { CustomerListComponent } from './features/billing/components/customer-list/customer-list.component'
import { CustomerDetailsComponent } from './features/billing/components/customer-details/customer-details.component'
import {
  CustomerDetailsUpdateComponent,
} from './features/billing/components/customer-details/customer-details-update/customer-details-update-dialog.component'
import { AddressEditDialogComponent } from './shared/components/address-dialog/address-edit-dialog.component'
import {
  BookingRuleListComponent,
} from './features/bookkeeping/components/booking-rule-list/booking-rule-list.component'
import {
  PaymentTransactionListComponent,
} from './features/payment/components/payment-transaction-list/payment-transaction-list.component'
import {
  PaymentTransactionDetailsComponent,
} from './features/payment/components/payment-transaction-details/payment-transaction-details.component'
import {
  NotificationListComponent,
} from './features/administration/components/notification-list/notification-list.component'
import { environment } from 'src/environments/environment'
import { BASE_PATH } from './backend-api'
import { LoginComponent } from './features/login/login.component'
import { AuthInterceptor } from './interceptors/auth.interceptor'
import { ErrorInterceptor } from './interceptors/error.interceptor'
import { MatPaginator, MatPaginatorIntl } from '@angular/material/paginator'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatMomentDateModule } from '@angular/material-moment-adapter'
import {
  PostingRecordListComponent,
} from './features/bookkeeping/components/postingrecord-list/posting-record-list.component'
import {
  PostingRecordDetailsComponent,
} from './features/bookkeeping/components/postingrecord-details/posting-record-details.component'
import {
  BookingRuleDetailsComponent,
} from './features/bookkeeping/components/booking-rule-details/booking-rule-details.component'
import { MatCheckbox } from '@angular/material/checkbox'
import { NgxEchartsModule } from 'ngx-echarts'
import {
  BookingAccountListComponent,
} from './features/bookkeeping/components/booking-account-list/booking-account-list.component'
import {
  CurrencyExchangeRateListComponent,
} from './features/bookkeeping/components/currency-exchange-rate-list/currency-exchange-rate-list.component'
import { OverviewTableComponent } from './shared/components/overview-table/overview-table.component'
import { MoneyComponent } from './shared/components/money/money.component'
import {
  WorkflowDetailsComponent,
} from './features/administration/components/workflow-details/workflow-details.component'
import { LocaleService } from './services/locale.service'
import {
  BusinessSegmentListComponent,
} from './features/billing/components/business-segment-list/business-segment-list.component'
import {
  BusinessSegmentDetailsComponent,
} from './features/billing/components/business-segment-details/business-segment-details.component'
import { ApprovalListComponent } from './features/billing/components/approval-list/approval-list.component'
import { ReportListComponent } from './features/billing/components/report-list/report-list.component'
import { UserListComponent } from './features/administration/components/user-list/user-list.component'
import { RoleListComponent } from './features/administration/components/role-list/role-list.component'
import { RoleDetailsComponent } from './features/administration/components/role-details/role-details.component'
import { UserDetailsComponent } from './features/administration/components/user-details/user-details.component'
import {
  PaymentAssignmentDetailsComponent,
} from './features/payment/components/payment-assignment-details/payment-assignment-details.component'
import {
  PaymentAssignmentListComponent,
} from './features/payment/components/payment-assignment-list/payment-assignment-list.component'
import { ThemeService } from './services/theme.service'
import { StyleManager } from './services/style-manager.service'
import { DocumentListComponent } from './features/billing/components/document-list/document-list.component'
import {
  SettlementReportListComponent,
} from './features/payment/components/settlement-report-list/settlement-report-list.component'
import {
  SettlementReportDetailsComponent,
} from './features/payment/components/settlement-report-details/settlement-report-details.component'
import { DetailsTableComponent } from './shared/components/details-table/details-table.component'
import { OpenItemListComponent } from './features/billing/components/open-item-list/open-item-list.component'
import { OpenItemDetailsComponent } from './features/billing/components/open-item-details/open-item-details.component'
import {
  PaymentAccountListComponent,
} from './features/payment/components/payment-account-list/payment-account-list.component'
import {
  PaymentCategorizationRuleListComponent,
} from './features/payment/components/payment-categorization-rule-list/payment-categorization-rule-list.component'
import {
  PaymentCategorizationRuleDetailsComponent,
} from './features/payment/components/payment-categorization-rule-details/payment-categorization-rule-details.component'
import { FailedJobListComponent } from './features/internal/components/failed-job-list/failed-job-list.component'
import {
  HistoricActivityListComponent,
} from './features/internal/components/historic-activity-list/historic-activity-list.component'
import {
  OrderSelectorDialogComponent,
} from './features/billing/components/order-selector-dialog/order-selector-dialog.component'
import { ApprovalDetailsComponent } from './features/billing/components/approval-details/approval-details.component'
import { DateFormattingPipe } from './shared/pipes/date.pipe'
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core'
import { TranslateHttpLoader } from '@ngx-translate/http-loader'
import { CustomMatPaginatorIntl } from './shared/components/mat-paginator-intl/custom-mat-paginator-intl'
import localeDe from '@angular/common/locales/de'
import {
  ManualBookingDetailsComponent,
} from './features/bookkeeping/components/manual-booking-details/manual-booking-details.component'
import { FormValidationService } from './services/form-validation.service'
import { MoneyInputComponent } from './shared/components/money-input/money-input.component'
import {
  ReportConfigurationListComponent,
} from './features/administration/components/report-configuration-list/report-configuration-list.component'
import {
  ReportConfigurationDetailsComponent,
} from './features/administration/components/report-configuration-details/report-configuration-details.component'
import {
  NotificationReceiverListComponent,
} from './features/administration/components/notification-receiver-list/notification-receiver-list.component'
import {
  NotificationReceiverDetailsComponent,
} from './features/administration/components/notification-receiver-details/notification-receiver-details.component'
import {
  PaymentAccountDetailsComponent,
} from './features/payment/components/payment-account-details/payment-account-details.component'
import {
  BookingAccountDetailsComponent,
} from './features/bookkeeping/components/booking-account-details/booking-account-details.component'
import { DocumentDetailsComponent } from './features/billing/components/document-details/document-details.component'
import {
  SentArchiveListComponent,
} from './features/administration/components/sent-archive-list/sent-archive-list.component'
import {
  DocumentSelectorComponent,
} from './features/billing/components/document-selector-dialog/document-selector-dialog.component'
import {
  SentArchiveDetailsComponent,
} from './features/administration/components/sent-archive-details/sent-archive-details.component'
import { CdkTrapFocus } from '@angular/cdk/a11y'
import { MatTab, MatTabGroup } from '@angular/material/tabs'
import {
  TranslationListComponent,
} from './features/administration/components/translation-list/translation-list.component'
import {
  TranslationDetailsComponent,
} from './features/administration/components/translation-details/translation-details.component'
import { SuppliersListComponent } from './features/billing/components/suppliers-list/suppliers-list.component'
import { SuppliersDetailsComponent } from './features/billing/components/suppliers-details/suppliers-details.component'
import { SubscriptionListComponent } from './features/billing/components/subscription-list/subscription-list.component'
import { SubscriptionDetailsComponent } from './features/billing/components/subscription-details/subscription-details.component'
import {
  SupplierDetailsUpdateComponent,
} from './features/billing/components/suppliers-details/supplier-details-update/supplier-details-update-dialog.component'
import { DetailsHeaderComponent } from './shared/components/details-header/details-header.component'
import {
  OrderCaptureDetailsComponent,
} from './features/billing/components/order-capture-details/order-capture-details.component'
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete'
import { AmountFormatterDirective } from './shared/directives/amount-formatter.directive'
import { CountNavigationsWebSocketService } from './services/count-navigations-web-socket.service'
import { NoteEditDialogComponent } from './shared/components/note-dialog/note-edit-dialog.component'
import {
  OrderTemplateAddDialogComponent,
} from './features/billing/components/order-capture-details/order-template-add-dialog/order-template-add-dialog.component'
import {
  OrderTemplateSelectorDialogComponent,
} from './features/billing/components/order-capture-details/order-template-selector-dialog/order-template-selector-dialog.component'
import {
  CustomerSelectorDialogComponent,
} from './features/billing/components/customer-selector-dialog/customer-selector-dialog.component'
import {OrderUpdateDetailsComponent} from "./features/billing/components/order-update-details/order-update-details.component";
import {ItemEditDialogComponent} from "./shared/components/item-edit/item-edit-dialog.component";
import {DocumentFileService} from "./services/document-file.service";

console.info('Angular CDK version', CDK_VERSION.full)
console.info('Angular Material version', MAT_VERSION.full)

registerLocaleData(localeDe)

// AoT requires an exported function for factories
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http)
}

export function provideCustomMatPaginatorIntlFactory(translate: TranslateService) {
  return new CustomMatPaginatorIntl(translate)
}

export function appInitializerFactory(translate: TranslateService) {
  return () =>
    new Promise<any>((resolve: Function) => {
      translate.setDefaultLang('en')
      translate.use('de').subscribe({
        next: () => {
          console.info(`Successfully initialized '${translate.currentLang}' language.'`)
          resolve(null)
        },
        error: (err) => {
          console.error(`Problem with '${translate.currentLang}' language initialization.'`, err)
          resolve(null)
        },
      })
    })
}

@NgModule({
  declarations: [
    AppComponent,
    // Shared Components
    OverviewTableComponent,
    DetailsTableComponent,
    DetailsHeaderComponent,
    MoneyComponent,
    MoneyInputComponent,
    // Feature Components
    DashboardComponent,
    UserSettingsComponent,
    OrderListComponent,
    OrderDetailsComponent,
    OrderUpdateDetailsComponent,
    OrderSelectorDialogComponent,
    CustomerSelectorDialogComponent,
    OrderTemplateSelectorDialogComponent,
    OrderTemplateAddDialogComponent,
    DocumentSelectorComponent,
    PdfViewerDialogComponent,
    ConfirmDialogComponent,
    CustomerListComponent,
    CustomerDetailsComponent,
    CustomerDetailsUpdateComponent,
    AddressEditDialogComponent,
    ItemEditDialogComponent,
    NoteEditDialogComponent,
    SuppliersListComponent,
    SuppliersDetailsComponent,
    SupplierDetailsUpdateComponent,
    SubscriptionListComponent,
    SubscriptionDetailsComponent,
    BookingRuleListComponent,
    BookingRuleDetailsComponent,
    BookingAccountListComponent,
    BookingAccountDetailsComponent,
    CurrencyExchangeRateListComponent,
    PaymentTransactionListComponent,
    PaymentTransactionDetailsComponent,
    PaymentAssignmentListComponent,
    PaymentAssignmentDetailsComponent,
    NotificationListComponent,
    LoginComponent,
    PostingRecordListComponent,
    PostingRecordDetailsComponent,
    WorkflowDetailsComponent,
    BusinessSegmentListComponent,
    BusinessSegmentDetailsComponent,
    ApprovalListComponent,
    ReportListComponent,
    UserListComponent,
    UserDetailsComponent,
    RoleListComponent,
    RoleDetailsComponent,
    DocumentListComponent,
    DocumentDetailsComponent,
    SettlementReportListComponent,
    SettlementReportDetailsComponent,
    OpenItemListComponent,
    OpenItemDetailsComponent,
    PaymentAccountListComponent,
    PaymentAccountDetailsComponent,
    PaymentCategorizationRuleListComponent,
    PaymentCategorizationRuleDetailsComponent,
    FailedJobListComponent,
    HistoricActivityListComponent,
    ApprovalDetailsComponent,
    DateFormattingPipe,
    ManualBookingDetailsComponent,
    ReportConfigurationListComponent,
    ReportConfigurationDetailsComponent,
    NotificationReceiverListComponent,
    NotificationReceiverDetailsComponent,
    SentArchiveListComponent,
    SentArchiveDetailsComponent,
    TranslationListComponent,
    TranslationDetailsComponent,
    AmountFormatterDirective,
    OrderCaptureDetailsComponent,
  ],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      defaultLanguage: 'en',
    }),
    FormsModule,
    ReactiveFormsModule,
    // Material Modules
    MatButtonModule,
    MatCardModule,
    MatMenuModule,
    MatIconModule,
    MatToolbarModule,
    MatTooltipModule,
    MatDividerModule,
    MatListModule,
    MatFormFieldModule,
    MatInputModule,
    MatMomentDateModule,
    MatGridListModule,
    MatTableModule,
    MatSelectModule,
    MatDialogModule,
    MatSnackBarModule,
    MatSidenavModule,
    MatDatepickerModule,
    MatExpansionModule,
    MatProgressSpinnerModule,
    MatSortModule,
    BrowserAnimationsModule,
    MatPaginator,
    MatCheckbox,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts'),
    }),
    NgOptimizedImage,
    CdkTrapFocus,
    MatTabGroup,
    MatTab,
    MatAutocomplete,
    MatAutocompleteTrigger,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
    provideAnimations(),
    ThemeService,
    FormValidationService,
    DocumentFileService,
    CountNavigationsWebSocketService,
    StyleManager,
    importProvidersFrom(MatNativeDateModule),
    {
      provide: LOCALE_ID,
      deps: [LocaleService],
      useFactory: (localeService: LocaleService) => localeService.getLocale(),
    },
    {
      provide: MAT_DATE_LOCALE,
      deps: [LocaleService],
      useFactory: (localeService: LocaleService) => localeService.getLocale(),
    },
    { provide: BASE_PATH, useValue: environment.backendBaseUrl },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: MatPaginatorIntl, useClass: CustomMatPaginatorIntl },
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: { floatLabel: 'always', appearance: 'outline' },
    },
    {
      provide: MatPaginatorIntl,
      useFactory: provideCustomMatPaginatorIntlFactory,
      deps: [TranslateService],
    },
    {
      provide: MATERIAL_SANITY_CHECKS,
      useValue: false,
    },
    provideHttpClient(withInterceptorsFromDi()),
    DecimalPipe,
  ],
  exports: [DateFormattingPipe],
})
export class AppModule {
}

/* eslint-enable @typescript-eslint/no-extraneous-class */
/* eslint-enable @typescript-eslint/no-unsafe-function-type */
