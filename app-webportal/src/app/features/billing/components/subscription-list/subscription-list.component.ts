import { Component, ViewChild } from '@angular/core'
import { WebPortalSubscriptionService, WebPortalSubscriptionListEntry, UserRight } from 'src/app/backend-api'
import { SessionService } from '../../../../services/session.service'
import { Column, CustomColumn, DetailsLinkColumn, StringColumn } from '../../../../shared/models/column'
import { OverviewTableComponent } from '../../../../shared/components/overview-table/overview-table.component'
import { TranslateService } from '@ngx-translate/core'

@Component({
  standalone: false,
  selector: 'app-subscription-list',
  templateUrl: './subscription-list.component.html',
  styleUrls: ['./subscription-list.component.css'],
})
export class SubscriptionListComponent {
  @ViewChild(OverviewTableComponent)
  overviewTableComponent: OverviewTableComponent

  protected readonly UserRight = UserRight

  allColumns: Column[] = [
    new DetailsLinkColumn('key', 'Key', true, 'key', '/subscriptions', 'key'),
    new StringColumn('name', this.translate.instant('SUBSCRIPTIONS.NAME'), true, 'name'),
    new StringColumn('customerKey', this.translate.instant('SUBSCRIPTIONS.CUSTOMER'), true, 'customerKey'),
    new StringColumn('status', this.translate.instant('SUBSCRIPTIONS.STATUS'), true, 'status'),
    new StringColumn('frequency', this.translate.instant('SUBSCRIPTIONS.FREQUENCY'), true, 'frequency'),
    new CustomColumn(
      'amount',
      this.translate.instant('SUBSCRIPTIONS.AMOUNT'),
      true,
      '',
      false,
      null,
      (row) => this.formatAmount(row)
    ),
    new StringColumn('nextBillingDate', this.translate.instant('SUBSCRIPTIONS.NEXT_BILLING_DATE'), true, 'nextBillingDate'),
  ]

  constructor(
    private webPortalSubscriptionService: WebPortalSubscriptionService,
    private sessionService: SessionService,
    private translate: TranslateService
  ) {}

  loadSubscriptions(event: any) {
    const observable = this.webPortalSubscriptionService.loadSubscriptions(
      this.sessionService.getTenantKey(),
      event.pageIndex,
      event.pageSize,
      event.sort,
      event.direction,
      event.filterText
    )
    this.overviewTableComponent.loadedData(observable)
  }

  formatAmount(subscription: WebPortalSubscriptionListEntry): string {
    return `${subscription.amount} ${subscription.currency}`
  }
}
