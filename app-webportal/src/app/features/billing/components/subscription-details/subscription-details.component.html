<div class="page-container">
  <div class="page-header">
    <div class="header-left">
      <button mat-icon-button routerLink="/subscriptions" class="back-button">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>
        {{ isNewMode ? ('SUBSCRIPTIONS.CREATE_NEW' | translate) :
           isEditMode ? ('SUBSCRIPTIONS.EDIT' | translate) :
           ('SUBSCRIPTIONS.DETAILS' | translate) }}
      </h1>
    </div>
    <div class="header-actions" *ngIf="!isNewMode && !isEditMode">
      <button mat-raised-button color="primary" (click)="onEdit()">
        <mat-icon>edit</mat-icon>
        {{ 'COMMON.EDIT' | translate }}
      </button>
      <button mat-raised-button color="warn" (click)="onDelete()">
        <mat-icon>delete</mat-icon>
        {{ 'COMMON.DELETE' | translate }}
      </button>
    </div>
  </div>

  <mat-card class="form-card">
    <mat-card-content>
      <form [formGroup]="subscriptionForm" (ngSubmit)="onSave()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.KEY' | translate }}</mat-label>
            <input matInput formControlName="key" [readonly]="!isNewMode">
            <mat-error *ngIf="subscriptionForm.get('key')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="subscriptionForm.get('key')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 100} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.NAME' | translate }}</mat-label>
            <input matInput formControlName="name">
            <mat-error *ngIf="subscriptionForm.get('name')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="subscriptionForm.get('name')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 200} }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field-full">
            <mat-label>{{ 'SUBSCRIPTIONS.DESCRIPTION' | translate }}</mat-label>
            <textarea matInput formControlName="description" rows="3"></textarea>
            <mat-error *ngIf="subscriptionForm.get('description')?.hasError('maxlength')">
              {{ 'VALIDATION.MAX_LENGTH' | translate: {max: 500} }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.CUSTOMER' | translate }}</mat-label>
            <input matInput formControlName="customerKey" [matAutocomplete]="customerAuto">
            <mat-autocomplete #customerAuto="matAutocomplete" [displayWith]="displayCustomer">
              <mat-option *ngFor="let customer of filteredCustomers | async" [value]="customer.key">
                {{ displayCustomer(customer) }}
              </mat-option>
            </mat-autocomplete>
            <mat-error *ngIf="subscriptionForm.get('customerKey')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.STATUS' | translate }}</mat-label>
            <mat-select formControlName="status">
              <mat-option *ngFor="let status of subscriptionStatuses" [value]="status">
                {{ 'SUBSCRIPTION_STATUS.' + status | translate }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="subscriptionForm.get('status')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.FREQUENCY' | translate }}</mat-label>
            <mat-select formControlName="frequency">
              <mat-option *ngFor="let frequency of subscriptionFrequencies" [value]="frequency">
                {{ 'SUBSCRIPTION_FREQUENCY.' + frequency | translate }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="subscriptionForm.get('frequency')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.AMOUNT' | translate }}</mat-label>
            <input matInput type="number" formControlName="amount" step="0.01" min="0">
            <mat-error *ngIf="subscriptionForm.get('amount')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
            <mat-error *ngIf="subscriptionForm.get('amount')?.hasError('min')">
              {{ 'VALIDATION.MIN_VALUE' | translate: {min: 0} }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.CURRENCY' | translate }}</mat-label>
            <mat-select formControlName="currency">
              <mat-option *ngFor="let currency of currencies" [value]="currency">
                {{ currency }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="subscriptionForm.get('currency')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.START_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="startDatePicker" formControlName="startDate">
            <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #startDatePicker></mat-datepicker>
            <mat-error *ngIf="subscriptionForm.get('startDate')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.END_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="endDatePicker" formControlName="endDate">
            <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #endDatePicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'SUBSCRIPTIONS.NEXT_BILLING_DATE' | translate }}</mat-label>
            <input matInput [matDatepicker]="nextBillingDatePicker" formControlName="nextBillingDate">
            <mat-datepicker-toggle matSuffix [for]="nextBillingDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #nextBillingDatePicker></mat-datepicker>
            <mat-error *ngIf="subscriptionForm.get('nextBillingDate')?.hasError('required')">
              {{ 'VALIDATION.REQUIRED' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-actions" *ngIf="isEditMode || isNewMode">
          <button mat-raised-button type="button" (click)="onCancel()">
            {{ 'COMMON.CANCEL' | translate }}
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="loading">
            <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
            {{ isNewMode ? ('COMMON.CREATE' | translate) : ('COMMON.SAVE' | translate) }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
