import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { MatSnackBar } from '@angular/material/snack-bar'
import { TranslateService } from '@ngx-translate/core'
import {
  WebPortalSubscriptionService,
  WebPortalSubscriptionDetailsData,
  WebPortalCustomerService,
  WebPortalCustomerListEntry,
  SubscriptionStatus,
  SubscriptionFrequency,
  Currency
} from 'src/app/backend-api'
import { SessionService } from '../../../../services/session.service'
import { Observable, of } from 'rxjs'
import { map, startWith } from 'rxjs/operators'

@Component({
  standalone: false,
  selector: 'app-subscription-details',
  templateUrl: './subscription-details.component.html',
  styleUrls: ['./subscription-details.component.css'],
})
export class SubscriptionDetailsComponent implements OnInit {
  subscriptionForm: FormGroup
  subscriptionKey: string
  isEditMode = false
  isNewMode = false
  loading = false
  customers: WebPortalCustomerListEntry[] = []
  filteredCustomers: Observable<WebPortalCustomerListEntry[]>

  subscriptionStatuses = Object.values(SubscriptionStatus)
  subscriptionFrequencies = Object.values(SubscriptionFrequency)
  currencies = Object.values(Currency)

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder,
    private webPortalSubscriptionService: WebPortalSubscriptionService,
    private webPortalCustomerService: WebPortalCustomerService,
    private sessionService: SessionService,
    private snackBar: MatSnackBar,
    private translate: TranslateService
  ) {
    this.subscriptionForm = this.createForm()
  }

  ngOnInit(): void {
    this.subscriptionKey = this.route.snapshot.paramMap.get('key')!
    this.isNewMode = this.subscriptionKey === 'new'
    this.isEditMode = this.route.snapshot.url.some(segment => segment.path === 'edit')

    this.loadCustomers()
    this.setupCustomerFilter()

    if (!this.isNewMode) {
      this.loadSubscription()
    } else {
      this.subscriptionForm.patchValue({
        key: this.generateSubscriptionKey(),
        status: SubscriptionStatus.Active,
        frequency: SubscriptionFrequency.Monthly,
        currency: Currency.Eur
      })
    }

    if (!this.isEditMode && !this.isNewMode) {
      this.subscriptionForm.disable()
    }
  }

  private createForm(): FormGroup {
    return this.formBuilder.group({
      key: ['', [Validators.required, Validators.maxLength(100)]],
      name: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', Validators.maxLength(500)],
      customerKey: ['', Validators.required],
      status: ['', Validators.required],
      frequency: ['', Validators.required],
      amount: ['', [Validators.required, Validators.min(0)]],
      currency: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: [''],
      nextBillingDate: ['', Validators.required],
      version: [0]
    })
  }

  private loadCustomers(): void {
    this.webPortalCustomerService.loadCustomers(
      this.sessionService.getTenantKey(),
      0,
      1000,
      'name',
      'asc'
    ).subscribe({
      next: (response) => {
        this.customers = response.items || []
      },
      error: (error) => {
        console.error('Error loading customers:', error)
        this.showError('SUBSCRIPTIONS.ERROR_LOADING_CUSTOMERS')
      }
    })
  }

  private setupCustomerFilter(): void {
    this.filteredCustomers = this.subscriptionForm.get('customerKey')!.valueChanges.pipe(
      startWith(''),
      map(value => {
        if (typeof value === 'string') {
          return this.filterCustomers(value)
        } else if (value && typeof value === 'object') {
          // If it's a customer object, return all customers
          return this.customers
        }
        return this.customers
      })
    )
  }

  private filterCustomers(value: string): WebPortalCustomerListEntry[] {
    if (!value) return this.customers
    const filterValue = value.toLowerCase()
    return this.customers.filter(customer => {
      const customerName = this.getCustomerDisplayName(customer)
      return customerName.toLowerCase().includes(filterValue) ||
        customer.key.toLowerCase().includes(filterValue)
    })
  }

  displayCustomer(value: any): string {
    if (typeof value === 'string') {
      // If it's just a key, find the customer and display it
      const customer = this.customers.find(c => c.key === value)
      return customer ? `${this.getCustomerDisplayName(customer)} (${customer.key})` : value
    } else if (value && typeof value === 'object') {
      // If it's a customer object
      return `${this.getCustomerDisplayName(value)} (${value.key})`
    }
    return ''
  }

  private getCustomerDisplayName(customer: WebPortalCustomerListEntry): string {
    if (customer.companyName) {
      return customer.companyName
    } else if (customer.firstName && customer.lastName) {
      return `${customer.firstName} ${customer.lastName}`
    } else if (customer.firstName) {
      return customer.firstName
    } else if (customer.lastName) {
      return customer.lastName
    }
    return customer.key
  }

  private loadSubscription(): void {
    this.loading = true
    this.webPortalSubscriptionService.loadSubscription(
      this.sessionService.getTenantKey(),
      this.subscriptionKey
    ).subscribe({
      next: (response) => {
        this.subscriptionForm.patchValue(response.subscriptionData)
        this.loading = false
      },
      error: (error) => {
        console.error('Error loading subscription:', error)
        this.showError('SUBSCRIPTIONS.ERROR_LOADING')
        this.loading = false
      }
    })
  }

  onSave(): void {
    if (this.subscriptionForm.invalid) {
      this.markFormGroupTouched()
      return
    }

    this.loading = true
    const subscriptionData: WebPortalSubscriptionDetailsData = this.subscriptionForm.value

    const operation = this.isNewMode
      ? this.webPortalSubscriptionService.createSubscription(this.sessionService.getTenantKey(), subscriptionData)
      : this.webPortalSubscriptionService.updateSubscription(
          this.sessionService.getTenantKey(),
          this.subscriptionKey,
          subscriptionData
        )

    operation.subscribe({
      next: (response) => {
        this.showSuccess(this.isNewMode ? 'SUBSCRIPTIONS.CREATED_SUCCESS' : 'SUBSCRIPTIONS.UPDATED_SUCCESS')
        this.router.navigate(['/subscriptions', response.subscriptionData!.key])
        this.loading = false
      },
      error: (error) => {
        console.error('Error saving subscription:', error)
        this.showError(this.isNewMode ? 'SUBSCRIPTIONS.ERROR_CREATING' : 'SUBSCRIPTIONS.ERROR_UPDATING')
        this.loading = false
      }
    })
  }

  onEdit(): void {
    this.router.navigate(['/subscriptions', this.subscriptionKey, 'edit'])
  }

  onDelete(): void {
    if (confirm(this.translate.instant('SUBSCRIPTIONS.CONFIRM_DELETE'))) {
      this.loading = true
      this.webPortalSubscriptionService.deleteSubscription(
        this.sessionService.getTenantKey(),
        this.subscriptionKey
      ).subscribe({
        next: () => {
          this.showSuccess('SUBSCRIPTIONS.DELETED_SUCCESS')
          this.router.navigate(['/subscriptions'])
          this.loading = false
        },
        error: (error) => {
          console.error('Error deleting subscription:', error)
          this.showError('SUBSCRIPTIONS.ERROR_DELETING')
          this.loading = false
        }
      })
    }
  }

  onCancel(): void {
    if (this.isNewMode) {
      this.router.navigate(['/subscriptions'])
    } else {
      this.router.navigate(['/subscriptions', this.subscriptionKey])
    }
  }

  private generateSubscriptionKey(): string {
    return 'SUB-' + Date.now()
  }

  private markFormGroupTouched(): void {
    Object.keys(this.subscriptionForm.controls).forEach(key => {
      this.subscriptionForm.get(key)?.markAsTouched()
    })
  }

  private showSuccess(messageKey: string): void {
    this.snackBar.open(this.translate.instant(messageKey), '', {
      duration: 3000,
      panelClass: ['success-snackbar']
    })
  }

  private showError(messageKey: string): void {
    this.snackBar.open(this.translate.instant(messageKey), '', {
      duration: 5000,
      panelClass: ['error-snackbar']
    })
  }
}
