import {
  AfterViewInit,
  Component,
  inject,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  DOCUMENT
} from '@angular/core'
import { SessionService } from './services/session.service'
import { TargetType, UserLanguage, UserRight, WebPortalOrderService } from './backend-api'
import { NavbarItem, NavbarSection } from './navbar-model'
import { ThemeService } from './services/theme.service'
import { TranslateService } from '@ngx-translate/core'
import { MatSidenav } from '@angular/material/sidenav'
import { LocaleService } from './services/locale.service'
import { UserLanguageExtended } from './shared/models/UserLanguageExtended'
import { TimeZoneService } from './services/timeZone.service'

import { Router } from '@angular/router'
import { debounceTime, map, Observable, of, Subject, Subscription, takeUntil } from 'rxjs'
import { CountNavigationsWebSocketService } from './services/count-navigations-web-socket.service'
import { FormBuilder, FormGroup } from '@angular/forms'

@Component({
  standalone: false,
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  username: string
  selectedTenant: string
  tenants: string[]

  form: FormGroup

  loadedEntityReferences: Observable<EntityReference[]>

  protected readonly UserRight = UserRight

  @ViewChild('sidenav') sidenav: MatSidenav

  navbarSections: NavbarSection[] = []

  initMenu() {
    this.navbarSections = [
      {
        name: this.translate.instant('MENU.BILLING'),
        icon: 'receipt',
        items: [
          {
            name: this.translate.instant('MENU.CUSTOMERS'),
            icon: null,
            routerLink: '/customers',
            right: UserRight.CustomersRead,
          },
          {
            name: this.translate.instant('MENU.SUPPLIERS'),
            icon: null,
            routerLink: '/suppliers',
            right: UserRight.SuppliersRead,
          },
          {
            name: this.translate.instant('MENU.SUBSCRIPTIONS'),
            icon: null,
            routerLink: '/subscriptions',
            right: UserRight.CustomersRead, // Using CustomersRead as subscription right for now
          },
          {
            name: this.translate.instant(
              this.sessionService.isMedicalUser() ? 'MENU.ORDER_CAPTURE_MEDICAL' : 'MENU.ORDER_CAPTURE'
            ),
            icon: null,
            routerLink: '/order-capture',
            targetKey: 'new',
            right: UserRight.OrderCapture,
          },
          {
            name: this.translate.instant(this.sessionService.isMedicalUser() ? 'MENU.ORDERS_MEDICAL' : 'MENU.ORDERS'),
            icon: null,
            routerLink: '/orders',
            right: UserRight.OrdersRead,
          },
          {
            name: this.translate.instant('MENU.OPEN_ITEMS'),
            icon: null,
            routerLink: '/open-items',
            right: UserRight.OpenItemsRead,
            badgeValue: 0,
          },
          {
            name: this.translate.instant('MENU.DOCUMENTS'),
            icon: null,
            routerLink: '/documents',
            right: UserRight.DocumentsRead,
          },
          {
            name: this.translate.instant('MENU.REPORTS'),
            icon: null,
            routerLink: '/reports',
            right: UserRight.ReportsRead,
          },
          {
            name: this.translate.instant('MENU.BUSINESS_SEGMENTS'),
            icon: null,
            routerLink: '/business-segments',
            right: UserRight.BusinessSegmentsRead,
          },
          {
            name: this.translate.instant('MENU.APPROVALS'),
            icon: null,
            routerLink: '/approvals',
            right: UserRight.ApprovalsRead,
            badgeValue: 0,
          },
        ],
      },
      {
        name: this.translate.instant('MENU.PAYMENT'),
        icon: 'payment',
        items: [
          {
            name: this.translate.instant('MENU.PAYMENT_ACCOUNTS'),
            icon: null,
            routerLink: '/payment-accounts',
            right: UserRight.PaymentAccountsRead,
          },
          {
            name: this.translate.instant('MENU.PAYMENT_CATEGORIZATION_RULES'),
            icon: null,
            routerLink: '/payment-categorization-rules',
            right: UserRight.PaymentCategorizationRulesRead,
          },
          {
            name: this.translate.instant('MENU.SETTLEMENT_REPORTS'),
            icon: null,
            routerLink: '/settlement-reports',
            right: UserRight.SettlementReportsRead,
          },
          {
            name: this.translate.instant('MENU.PAYMENT_TRANSACTIONS'),
            icon: null,
            routerLink: '/payment-transactions',
            right: UserRight.PaymentTransactionsRead,
            badgeValue: 0,
          },
          {
            name: this.translate.instant('MENU.PAYMENT_ASSIGNMENTS'),
            icon: null,
            routerLink: '/payment-assignments',
            right: UserRight.PaymentAssignmentsRead,
          },
        ],
      },
      {
        name: this.translate.instant('MENU.BOOKKEEPING'),
        icon: 'booking',
        items: [
          {
            name: this.translate.instant('MENU.BOOKING_ACCOUNTS'),
            icon: null,
            routerLink: '/booking-accounts',
            right: UserRight.BookingAccountsRead,
          },
          {
            name: this.translate.instant('MENU.BOOKING_RULES'),
            icon: null,
            routerLink: '/booking-rules',
            right: UserRight.BookingRulesRead,
          },
          {
            name: this.translate.instant('MENU.POSTING_RECORDS'),
            icon: null,
            routerLink: '/posting-records',
            right: UserRight.PostingRecordsRead,
          },
          {
            name: this.translate.instant('MENU.CURRENCY_EXCHANGE_RATES'),
            icon: null,
            routerLink: '/currency-exchange-rates',
            right: UserRight.CurrencyExchangeRateRead,
          },
          {
            name: this.translate.instant('MENU.MANUAL_BOOKING'),
            icon: null,
            routerLink: '/manual-bookings',
            targetKey: 'new',
            right: UserRight.ManualBooking,
          },
        ],
      },
      {
        name: this.translate.instant('MENU.ADMINISTRATION'),
        icon: 'settings_outline',
        items: [
          {
            name: this.translate.instant('MENU.SENT_ARCHIVE'),
            icon: null,
            routerLink: '/sent-archive',
            right: UserRight.SentArchiveRead,
          },
          {
            name: this.translate.instant('MENU.NOTIFICATIONS'),
            icon: null,
            routerLink: '/notifications',
            right: UserRight.NotificationsRead,
          },
          {
            name: this.translate.instant('MENU.NOTIFICATION_RECEIVERS'),
            icon: null,
            routerLink: '/notification-receivers',
            right: UserRight.NotificationReceiversRead,
          },
          {
            name: this.translate.instant('MENU.TRANSLATIONS'),
            icon: null,
            routerLink: '/translations',
            right: UserRight.TranslationsRead,
          },
          {
            name: this.translate.instant('MENU.REPORT_CONFIGURATIONS'),
            icon: null,
            routerLink: '/report-configurations',
            right: UserRight.ReportConfigurationsRead,
          },
          {
            name: this.translate.instant('MENU.USERS'),
            icon: null,
            routerLink: '/users',
            right: UserRight.UsersRead,
          },
          {
            name: this.translate.instant('MENU.ROLES'),
            icon: null,
            routerLink: '/roles',
            right: UserRight.RolesRead,
          },
          {
            name: this.translate.instant('MENU.WORKFLOW'),
            icon: null,
            routerLink: '/workflow',
            right: UserRight.WorkflowRead,
          },
        ],
      },
      {
        name: this.translate.instant('MENU.INTERNAL'),
        icon: 'shield_person',
        items: [
          {
            name: this.translate.instant('MENU.FAILED_JOBS'),
            icon: null,
            routerLink: '/failed-jobs',
            right: UserRight.InternalJobsRead,
            badgeValue: 0,
          },
          {
            name: this.translate.instant('MENU.HISTORIC_ACTIVITIES'),
            icon: null,
            routerLink: '/historic-activities',
            right: UserRight.InternalJobsRead,
          },
        ],
      },
    ]

    if (this.sidenav !== undefined) {
      this.sidenav.toggle(true).catch((error) => {
        console.error('Error toggling sidenav', error)
      })
    }
  }

  logoPath = ''

  private destroy$ = new Subject<void>()

  firstPageScroll = true

  private messagesSubscription: Subscription

  constructor(
    protected sessionService: SessionService,
    private themeService: ThemeService,
    private translate: TranslateService,
    private localeService: LocaleService,
    private timeZoneService: TimeZoneService,
    private countNavigationsWebSocketService: CountNavigationsWebSocketService,
    @Inject(DOCUMENT) private document: Document,
    private route: Router,
    private webPortalOrderService: WebPortalOrderService,
    protected fb: FormBuilder = inject(FormBuilder)
  ) {
    console.log('app component constructor')

    this.initTranslate(UserLanguage.English)

    this.sessionService.getCurrentUserSubject().subscribe((currentUser) => {
      this.unsubscribeFromWebSocketMessages()
      if (currentUser) {
        console.log('current user: ', currentUser.userData)
        this.username = currentUser.userData.initials
        this.selectedTenant = currentUser.userData.tenantKey
        this.tenants = currentUser.tenants
        this.logoPath = 'assets/logos/logo-' + currentUser.userData.theme + '.png'

        this.initTranslate(currentUser.userData.language)

        this.localeService.setLocale(currentUser.userData.locale)
        this.themeService.setTheme(currentUser.userData.theme)
        this.timeZoneService.setTimeZone(currentUser.userData.timeZone)

        this.subscribeToWebSocketMessages()
      } else {
        console.log('no current user')
        this.unsubscribeFromWebSocketMessages()
        this.username = null
        this.selectedTenant = null
        this.tenants = []
        this.logoPath = ''
        this.themeService.setTheme('default')
      }
    })
  }

  private subscribeToWebSocketMessages(): void {
    if (!this.messagesSubscription) {
      this.countNavigationsWebSocketService.connect()
      this.messagesSubscription = this.countNavigationsWebSocketService.messages$.subscribe((message) => {
        this.updateBadgeValues(JSON.parse(message))
      })
    }
  }

  private unsubscribeFromWebSocketMessages(): void {
    if (this.messagesSubscription) {
      this.countNavigationsWebSocketService.close()
      this.messagesSubscription.unsubscribe()
      this.messagesSubscription = null
    }
  }

  updateBadgeValues(message: any) {
    this.navbarSections.forEach((section) => {
      section.items.forEach((item) => {
        if (this.showNavbarItem(item)) {
          switch (item.routerLink) {
            case '/open-items':
              item.badgeValue = message.openItemsCount
              break
            case '/approvals':
              item.badgeValue = message.approvalsCount
              break
            case '/payment-transactions':
              item.badgeValue = message.paymentTransactionsCount
              break
            case '/failed-jobs':
              item.badgeValue = message.failedJobsCount
              break
          }
        }
      })
    })
  }

  ngOnInit() {
    this.form = this.fb.group({ entitySearch: [''] })
    this.form
      .get('entitySearch')
      .valueChanges.pipe(debounceTime(200), takeUntil(this.destroy$))
      .subscribe((value) => {
        this.onSearch(value)
      })
  }

  ngAfterViewInit(): void {
    if (this.sidenav !== undefined) {
      this.sidenav.openedChange.pipe(takeUntil(this.destroy$)).subscribe((opened) => {
        if (opened && this.firstPageScroll === true) {
          const url = this.route.url
          const parts = url.split('/')
          const rootPart = parts[1]
          const activeItem = this.document.querySelector('#' + rootPart.replace('/', ''))

          if (activeItem) {
            activeItem.scrollIntoView({ behavior: 'smooth' })
            this.firstPageScroll = false
          }
        }
      })
    }
  }

  private initTranslate(language: UserLanguage) {
    // this language will be used as a fallback when a translation isn't found in the current language
    // this.translate.setDefaultLang('de');
    const value = UserLanguageExtended[language].locale
    this.translate.use(value)
    this.translate.getTranslation(value).subscribe(() => this.initMenu())
  }

  logout() {
    this.sessionService.logout()
  }

  onTenantChange(tenantKey: string) {
    console.log('changing tenant to: ', tenantKey)
    this.sessionService.changeTenant(tenantKey)
  }

  showNavbarItem(item: NavbarItem): boolean {
    return this.sessionService.hasRight(item.right)
  }

  showNavbarSection(section: NavbarSection): boolean {
    return section.items.some((item) => this.showNavbarItem(item))
  }

  ngOnDestroy() {
    if (this.messagesSubscription) {
      this.messagesSubscription.unsubscribe()
    }
    this.countNavigationsWebSocketService.close()
  }

  onSearch(searchKey: string) {
    if (searchKey && searchKey.trim().length > 1) {
      const filter = `key=like=${searchKey.trim()}%`

      this.loadedEntityReferences = this.webPortalOrderService
        .loadOrderKeys(this.sessionService.getTenantKey(), 0, 3, 'key', 'asc', filter)
        .pipe(map((orders) => orders.items.map((key) => new EntityReference(TargetType.Order, key))))
    } else {
      this.loadedEntityReferences = of([])
    }
  }

  entityReferenceAutocompleteDisplay(entityReference: EntityReference): string {
    if (entityReference) return entityReference.key
    return ''
  }

  onSelectEntityReference(entityReference: EntityReference) {
    console.log('Selected entity reference:', entityReference)

    let targetPage = ''
    switch (entityReference.targetType) {
      case TargetType.Customer:
        targetPage = '/customers'
        break
      case TargetType.Supplier:
        targetPage = '/suppliers'
        break
      case TargetType.Order:
        targetPage = '/orders'
        break
      case TargetType.Document:
        targetPage = '/documents'
        break
      case TargetType.PaymentTransaction:
        targetPage = '/payment-transactions'
        break
      case TargetType.PaymentAssignment:
        targetPage = '/payment-assignments'
        break
    }

    this.route
      .navigate([targetPage, entityReference.key])
      .then(() => {
        this.form.get('entitySearch').setValue('')
        window.location.reload()
      })
      .catch(console.error)
  }

  formatEntityReference(entityReference: EntityReference) {
    return `${this.translate.instant('ENUM.TargetType.' + entityReference.targetType)}: ${entityReference.key}`
  }
}

class EntityReference {
  constructor(
    public targetType: TargetType,
    public key: string
  ) {}
}
