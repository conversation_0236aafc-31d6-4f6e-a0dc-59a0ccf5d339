/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SubscriptionStatus } from './subscriptionStatus';
import { Currency } from './currency';
import { SubscriptionFrequency } from './subscriptionFrequency';


export interface WebPortalSubscriptionListEntry { 
    /**
     * The unique key of the subscription
     */
    key: string;
    /**
     * The name of the subscription
     */
    name: string;
    /**
     * The key of the customer who owns this subscription
     */
    customerKey: string;
    /**
     * The name of the customer
     */
    customerName?: string;
    status: SubscriptionStatus;
    frequency: SubscriptionFrequency;
    /**
     * The recurring amount for the subscription
     */
    amount: number;
    currency: Currency;
    /**
     * The next billing date for the subscription
     */
    nextBillingDate: string;
    /**
     * When the subscription was created
     */
    createdAt?: string;
}
export namespace WebPortalSubscriptionListEntry {
}


