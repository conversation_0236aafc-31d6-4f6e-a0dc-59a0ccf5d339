/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaxInformation } from './taxInformation';
import { ItemCategory } from './itemCategory';
import { DebitCreditIndicator } from './debitCreditIndicator';


export interface WebPortalPredefinedItem { 
    /**
     * The key of the predefined item
     */
    key: string;
    /**
     * The article number of the predefined item
     */
    articleNumber: string;
    /**
     * The item group of the predefined item
     */
    itemGroup?: string;
    category: ItemCategory;
    debitCreditIndicator: DebitCreditIndicator;
    /**
     * The name of the predefined item
     */
    name: string;
    /**
     * The description of the predefined item
     */
    description?: string;
    /**
     * The quantity of the predefined item
     */
    quantity: number;
    /**
     * The unit net amount of the predefined item
     */
    unitNetAmount: number;
    taxInformation: TaxInformation;
}
export namespace WebPortalPredefinedItem {
}


