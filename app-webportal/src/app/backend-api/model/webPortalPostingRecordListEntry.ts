/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';
import { DocumentType } from './documentType';
import { PostingRecordTargetType } from './postingRecordTargetType';


export interface WebPortalPostingRecordListEntry { 
    /**
     * The key of the posting record
     */
    key: string;
    /**
     * The number of the posting record
     */
    number: number;
    targetType?: PostingRecordTargetType;
    documentType?: DocumentType;
    /**
     * The key of the order
     */
    orderKey?: string;
    /**
     * The key of the document
     */
    targetKey?: string;
    /**
     * The booking date of the posting record
     */
    bookingDate: string;
    currency: Currency;
}
export namespace WebPortalPostingRecordListEntry {
}


