/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DebitCreditIndicator } from './debitCreditIndicator';
import { Currency } from './currency';


export interface WebPortalSettlementReportItemsListEntry { 
    key: string;
    debitCreditIndicator: DebitCreditIndicator;
    bookingDate: string;
    valueDate: string;
    receiver: string;
    sender: string;
    purpose: string;
    amount: number;
    currency: Currency;
}
export namespace WebPortalSettlementReportItemsListEntry {
}


