/**
 * billing-solution frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Currency } from './currency';
import { UserLanguage } from './userLanguage';


export interface WebPortalUserDetailsData { 
    tenantKey: string;
    locale: string;
    language: UserLanguage;
    theme: string;
    timeZone: string;
    key: string;
    initials: string;
    currency: Currency;
    defaultTaxRate: number;
    medical: boolean;
    version: number;
}
export namespace WebPortalUserDetailsData {
}


