{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"billing-solution": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist/billing-solution"}, "index": "src/index.html", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/assets/fonts", "src/assets/fonts/pdf"], "styles": ["src/styles.scss", {"input": "src/themes/default.scss", "bundleName": "default", "inject": false}, {"input": "src/themes/bikesale-uk.scss", "bundleName": "bikesale-uk", "inject": false}, {"input": "src/themes/sunpower.scss", "bundleName": "sunpower", "inject": false}, {"input": "src/themes/mvz-gesund.scss", "bundleName": "mvz-gesund", "inject": false}, {"input": "src/themes/velocity-wheels.scss", "bundleName": "velocity-wheels", "inject": false}], "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "outputHashing": "all", "allowedCommonJsDependencies": ["@aws-crypto/sha256-browser", "@aws-crypto/sha256-js", "@aws-crypto/crc32", "buffer", "uuid", "isomorphic-unfetch", "js-cookie", "cookie", "moment", "pdfmake/build/pdfmake", "lodash/get", "lodash/isEmpty", "lodash/isEqual", "url", "ulid", "camelcase-keys", "lodash/flatten", "lodash/noop", "echarts", "echarts/core", "echarts/charts", "echarts/components", "echarts/renderers"]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}], "allowedCommonJsDependencies": ["@aws-crypto/sha256-browser", "@aws-crypto/sha256-js", "@aws-crypto/crc32", "buffer", "uuid", "isomorphic-unfetch", "js-cookie", "cookie", "moment", "pdfmake/build/pdfmake", "lodash/get", "lodash/isEmpty", "lodash/isEqual", "url", "ulid", "camelcase-keys", "lodash/flatten", "lodash/noop"]}, "docker": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.docker.ts"}], "outputHashing": "all", "allowedCommonJsDependencies": ["@aws-crypto/sha256-browser", "@aws-crypto/sha256-js", "@aws-crypto/crc32", "buffer", "uuid", "isomorphic-unfetch", "js-cookie", "cookie", "moment", "pdfmake/build/pdfmake", "lodash/get", "lodash/isEmpty", "lodash/isEqual", "url", "ulid", "camelcase-keys", "lodash/flatten", "lodash/noop"]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "billing-solution:build:production"}, "development": {"buildTarget": "billing-solution:build:development"}, "docker": {"buildTarget": "billing-solution:build:docker"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "billing-solution:build"}}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/assets/fonts", "src/assets/fonts/pdf"], "styles": ["src/styles.scss", {"input": "src/themes/default.scss", "bundleName": "default", "inject": false}, {"input": "src/themes/bikesale-uk.scss", "bundleName": "bikesale-uk", "inject": false}, {"input": "src/themes/sunpower.scss", "bundleName": "sunpower", "inject": false}, {"input": "src/themes/mvz-gesund.scss", "bundleName": "mvz-gesund", "inject": false}], "scripts": []}}}}}, "cli": {"analytics": false}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}