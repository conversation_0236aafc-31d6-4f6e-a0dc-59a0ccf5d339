# Subscription Workflow Demo Script (PowerShell)
# This script demonstrates the subscription workflow functionality

Write-Host "🚗 Velocity Wheels Subscription Workflow Demo" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Check if the application is running
Write-Host "📋 Checking if the application is running..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-RestMethod -Uri "http://localhost:8080/actuator/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Application is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Application is not running. Please start the billing-solution application first." -ForegroundColor Red
    Write-Host "   Run: ./gradlew bootRun" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Run the Velocity Wheels subscription demo
Write-Host "🎯 Starting Velocity Wheels Subscription Workflow Demo..." -ForegroundColor Yellow
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/demo/subscription/velocity-wheels" -Method Get
    
    Write-Host "✅ Demo completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📊 Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3 | Write-Host
    Write-Host ""
    Write-Host "🔍 What happened:" -ForegroundColor Cyan
    Write-Host "  1. Created a new BMW X5 lease subscription" -ForegroundColor White
    Write-Host "  2. Started the Velocity Wheels subscription workflow" -ForegroundColor White
    Write-Host "  3. Processed through workflow steps:" -ForegroundColor White
    Write-Host "     - Subscription creation" -ForegroundColor Gray
    Write-Host "     - Subscription activation" -ForegroundColor Gray
    Write-Host "     - Billing processing" -ForegroundColor Gray
    Write-Host "     - Invoice creation" -ForegroundColor Gray
    Write-Host "     - Invoice sending" -ForegroundColor Gray
    Write-Host ""
    Write-Host "📈 The subscription is now active and ready for billing cycles!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎉 Demo completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Demo failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "📋 Error details: $($_.Exception)" -ForegroundColor Red
}

Write-Host ""
Write-Host "📚 For more information, see SUBSCRIPTION_WORKFLOW_README.md" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔧 Additional commands you can try:" -ForegroundColor Yellow
Write-Host "  - Check workflow instances: Invoke-RestMethod http://localhost:8080/api/workflow/instances" -ForegroundColor Gray
Write-Host "  - View subscription status: Invoke-RestMethod http://localhost:8080/api/subscriptions" -ForegroundColor Gray
Write-Host "  - Monitor processing status via the web portal" -ForegroundColor Gray
Write-Host ""
