package com.klosesoft.billingsolution.domain.logic.service.billing.mapping

import com.klosesoft.billingsolution.domain.logic.service.payment.EpcQrCodeGeneratorService
import com.klosesoft.billingsolution.domain.model.valueobject.Aggregation
import com.klosesoft.billingsolution.domain.model.valueobject.CustomerType
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentItemMode
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentType
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.persistence.model.entity.Address
import com.klosesoft.billingsolution.persistence.model.entity.Document
import com.klosesoft.billingsolution.persistence.model.entity.DocumentItem
import com.klosesoft.billingsolution.persistence.model.entity.Order
import com.klosesoft.billingsolution.persistence.model.entity.payment.PaymentAccount
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentAccountLoadService
import io.github.oshai.kotlinlogging.KotlinLogging
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.format.DateTimeFormatter

@Service
class VelocityWheelsDocumentToPdfMapping(
    private val translateService: TranslateService,
    private val customerLoadService: CustomerLoadService,
    private val epcQrCodeGeneratorService: EpcQrCodeGeneratorService,
    private val paymentAccountLoadService: PaymentAccountLoadService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
    private val orderLoadService: OrderLoadService,
) : DocumentToPdfMapping {
    private val logger = KotlinLogging.logger {}

    override suspend fun getTemplate(
        documentType: DocumentType,
    ): String {
        when (documentType) {
            DocumentType.DEPOSIT, DocumentType.FINAL, DocumentType.REVERSED -> {
                return "invoice_velocity_wheels.jrxml"
            }

            else -> throw IllegalArgumentException("$documentType is unknown for this mapping")
        }
    }

    override suspend fun getMapping(
        document: Document,
        documentItems: List<DocumentItem>,
        originatorAddress: Address,
        billingAddress: Address,
        shippingAddress: Address?,
        documentItemMode: DocumentItemMode,
    ): Map<String, Any> {
        val parameters: MutableMap<String, Any> = HashMap()
        val translationParameters = mutableMapOf<String, String>()

        val order = orderLoadService.findById(document.orderId)
        val customer = customerLoadService.findById(document.customerId)
        val language = translateService.determineLanguage(
            document.tenantId,
            customer.language,
            TranslationType.DOCUMENT,
        )

        parameters["merchantAddress"] = DocumentMappingHelper.buildOriginatorAddress(originatorAddress)
        parameters["billingAddress"] = DocumentMappingHelper.buildBillingAddress(billingAddress)

        val businessSegment = businessSegmentLoadService.findById(document.businessSegmentId)
        parameters["logo"] = businessSegment.logo

        parameters["header"] = fetchHeader(document, language)

        // Company information specific to Velocity Wheels
        parameters["companyName"] = "Velocity Wheels Ltd."
        parameters["companyPhone"] = "+44 20 7946 0958"
        parameters["companyEmail"] = "<EMAIL>"

        // Customer information
        when (customer.customerType) {
            CustomerType.PERSON -> {
                parameters["customerName"] = "${customer.firstName} ${customer.lastName}"
            }
            CustomerType.COMPANY -> {
                parameters["customerName"] = customer.companyName ?: ""
            }
        }

        // Document information
        parameters["invoiceNumber"] = document.key
        parameters["invoiceDate"] = document.documentDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        parameters["orderNumber"] = order.key

        // Lease-specific information from order properties
        parameters["vehicleVin"] = order.properties[PropertyKey.VIN] ?: ""
        parameters["leaseStartDate"] = order.deliveryDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        parameters["leasePeriod"] = calculateLeasePeriod(order)

        // Payment information
        val paymentAccounts = paymentAccountLoadService.findByTenantIdAndDefaultAccountAndPaymentAccountType(
            document.tenantId,
            true,
            PaymentAccountType.BANK_ACCOUNT,
        )

        putEpcQrCode(document, parameters, paymentAccounts)
        parameters["bankAccount"] = paymentAccounts.accountId
        parameters["bankName"] = paymentAccounts.name
        parameters["bic"] = paymentAccounts.bic ?: ""

        // Document items
        val itemDataSource = JRBeanCollectionDataSource(
            getItems(documentItems, language),
        )
        parameters["itemDataSource"] = itemDataSource

        // Totals
        val totalAggregation = document.getSumAggregations(Aggregation.TOTAL.name)
        parameters["netTotal"] = totalAggregation.netSum.amount
        parameters["taxTotal"] = totalAggregation.taxSum.amount
        parameters["grossTotal"] = totalAggregation.grossSum.amount

        // Translations
        parameters.putAll(getTranslations(document, language, translationParameters))

        return parameters
    }

    private suspend fun putEpcQrCode(
        document: Document,
        parameters: MutableMap<String, Any>,
        defaultPaymentAccount: PaymentAccount,
    ) {
        if (document.documentType != DocumentType.REVERSED) {
            val totalSum = document.getSumAggregations(Aggregation.TOTAL.name)

            val epcQrCode = epcQrCodeGeneratorService.generateEpcQrCode(
                defaultPaymentAccount,
                totalSum.grossSum.amount,
                totalSum.grossSum.currency,
                document.key,
            )

            parameters["epcQrCode"] = epcQrCode
        }
    }

    private suspend fun fetchHeader(
        document: Document,
        language: Language,
    ): String = when (document.documentType) {
        DocumentType.DEPOSIT -> translateService.translate(
            VelocityWheelsTranslationKey.HEADER_DEPOSIT.name,
            language,
            document.tenantId,
            TranslationType.DOCUMENT,
            emptyMap(),
        )

        DocumentType.FINAL -> translateService.translate(
            VelocityWheelsTranslationKey.HEADER_FINAL.name,
            language,
            document.tenantId,
            TranslationType.DOCUMENT,
            emptyMap(),
        )

        DocumentType.REVERSED -> translateService.translate(
            VelocityWheelsTranslationKey.HEADER_REVERSED.name,
            language,
            document.tenantId,
            TranslationType.DOCUMENT,
            emptyMap(),
        )

        else -> throw IllegalArgumentException("Document type ${document.documentType} is unknown for this mapping")
    }

    override suspend fun getAllParameters(): List<String> = VelocityWheelsTranslationKey.entries.map { it.name }

    private fun calculateLeasePeriod(order: Order): String {
        // Calculate lease period from order properties or default to standard terms
        val paymentDueDate = order.paymentDueDate
        val deliveryDate = order.deliveryDate
        val monthsDiff = java.time.Period.between(deliveryDate, paymentDueDate).toTotalMonths()
        return if (monthsDiff > 0) "$monthsDiff months" else "Monthly"
    }

    private fun getItems(
        documentItems: List<DocumentItem>,
        @Suppress("UNUSED_PARAMETER") language: Language,
    ): List<VelocityWheelsReportItem> = documentItems
        .filter { it.debitCreditIndicator == DebitCreditIndicator.DEBIT }
        .map { documentItem ->
            VelocityWheelsReportItem(
                description = documentItem.name,
                serviceType = getServiceType(documentItem.name),
                quantity = documentItem.quantity,
                unitPrice = documentItem.unitNetAmount,
                totalPrice = documentItem.unitNetAmount.multiply(documentItem.quantity),
                taxRate = documentItem.taxes.firstOrNull()?.taxRate ?: BigDecimal.ZERO,
                vehicleVin = documentItem.properties[PropertyKey.VIN] ?: "",
            )
        }

    private fun getServiceType(itemName: String): String {
        return when {
            itemName.contains("Lease", ignoreCase = true) -> "Vehicle Lease"
            itemName.contains("Maintenance", ignoreCase = true) -> "Maintenance Package"
            itemName.contains("Insurance", ignoreCase = true) -> "Insurance Package"
            itemName.contains("Service", ignoreCase = true) -> "Service Package"
            else -> "Subscription Service"
        }
    }

    private suspend fun getTranslations(
        document: Document,
        language: Language,
        translationParameters: Map<String, String>,
    ): Map<String, String> = VelocityWheelsTranslationKey.entries.associate { key ->
        key.name to translateService.translate(
            key.name,
            language,
            document.tenantId,
            TranslationType.DOCUMENT,
            translationParameters,
        )
    }

    data class VelocityWheelsReportItem(
        val description: String,
        val serviceType: String,
        val quantity: BigDecimal,
        val unitPrice: BigDecimal,
        val totalPrice: BigDecimal,
        val taxRate: BigDecimal,
        val vehicleVin: String,
    )

    enum class VelocityWheelsTranslationKey {
        HEADER_DEPOSIT,
        HEADER_FINAL,
        HEADER_REVERSED,
        CUSTOMER_LABEL,
        DATE_LABEL,
        ORDER_LABEL,
        INVOICE_NUMBER_LABEL,
        INVOICE_DATE_LABEL,
        VEHICLE_INFO_LABEL,
        VIN_LABEL,
        LEASE_PERIOD_LABEL,
        LEASE_START_LABEL,
        SERVICE_TYPE_LABEL,
        DESCRIPTION_LABEL,
        QUANTITY_LABEL,
        UNIT_PRICE_LABEL,
        TOTAL_PRICE_LABEL,
        NET_TOTAL_LABEL,
        TAX_LABEL,
        GROSS_TOTAL_LABEL,
        PAYMENT_INFO_LABEL,
        THANK_YOU_LABEL,
    }
}
