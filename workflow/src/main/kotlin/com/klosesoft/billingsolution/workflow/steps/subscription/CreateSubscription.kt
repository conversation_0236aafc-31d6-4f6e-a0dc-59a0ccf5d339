package com.klosesoft.billingsolution.workflow.steps.subscription

import com.klosesoft.billingsolution.workflow.steps.AbstractWorkflowStep
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
internal class CreateSubscription(private val target: CreateSubscriptionService) : AbstractWorkflowStep() {

    override suspend fun exec(
        execution: DelegateExecution,
    ) {
        target.exec(execution)
    }
}
