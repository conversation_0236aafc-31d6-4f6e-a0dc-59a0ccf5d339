package com.klosesoft.billingsolution.workflow.steps.subscription

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.service.common.SubscriptionService
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.workflow.api.parameters.SubscriptionWorkflowParameters
import io.github.oshai.kotlinlogging.KotlinLogging
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
internal class PauseSubscriptionService(
    private val subscriptionService: SubscriptionService,
) {
    private val logger = KotlinLogging.logger {}

    @Transactional(rollbackFor = [Throwable::class])
    suspend fun exec(
        execution: DelegateExecution,
    ) {
        val parameters = SubscriptionWorkflowParameters.fromMap(execution.variables)

        logger.info { "Pausing subscription ${parameters.subscriptionId}" }

        // Update subscription status to PAUSED
        subscriptionService.updateSubscriptionStatus(
            lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            subscriptionId = parameters.subscriptionId,
            status = SubscriptionStatus.PAUSED,
        )

        subscriptionService.updateSubscriptionProcessingStatus(
            lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            subscriptionId = parameters.subscriptionId,
            processingStatus = ProcessingStatus.SUBSCRIPTION_PAUSED,
        )
    }
}
