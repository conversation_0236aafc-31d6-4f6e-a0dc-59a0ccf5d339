package com.klosesoft.billingsolution.workflow.steps.subscription

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.service.common.SubscriptionService
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.workflow.api.parameters.SubscriptionWorkflowParameters
import io.github.oshai.kotlinlogging.KotlinLogging
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
internal class SendSubscriptionInvoiceService(
    private val subscriptionService: SubscriptionService,
) {
    private val logger = KotlinLogging.logger {}

    @Transactional(rollbackFor = [Throwable::class])
    suspend fun exec(
        execution: DelegateExecution,
    ) {
        val parameters = SubscriptionWorkflowParameters.fromMap(execution.variables)

        logger.info { "Sending invoice email for subscription ${parameters.subscriptionId}" }

        // TODO: Implement email sending logic
        // This would typically involve:
        // 1. Loading the subscription and customer details
        // 2. Generating the invoice PDF
        // 3. Sending email with invoice attachment
        // For now, we'll just update the processing status

        subscriptionService.updateSubscriptionProcessingStatus(
            lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            subscriptionId = parameters.subscriptionId,
            processingStatus = ProcessingStatus.SUBSCRIPTION_INVOICE_SENT,
        )
    }
}
