package com.klosesoft.billingsolution.workflow.steps.subscription

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.service.common.SubscriptionService
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.workflow.api.parameters.SubscriptionWorkflowParameters
import io.github.oshai.kotlinlogging.KotlinLogging
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Service
internal class ProcessSubscriptionBillingService(
    private val subscriptionService: SubscriptionService,
    private val subscriptionLoadService: SubscriptionLoadService,
) {
    private val logger = KotlinLogging.logger {}

    @Transactional(rollbackFor = [Throwable::class])
    suspend fun exec(
        execution: DelegateExecution,
    ) {
        val parameters = SubscriptionWorkflowParameters.fromMap(execution.variables)

        logger.info { "Processing billing for subscription ${parameters.subscriptionId}" }

        val subscription = subscriptionLoadService.findById(parameters.subscriptionId)

        // Calculate next billing date based on frequency
        val nextBillingDate = calculateNextBillingDate(subscription.nextBillingDate, subscription.frequency)

        // Update next billing date
        subscriptionService.updateSubscriptionNextBillingDate(
            lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            subscriptionId = parameters.subscriptionId,
            nextBillingDate = nextBillingDate,
        )

        subscriptionService.updateSubscriptionProcessingStatus(
            lastModifiedBy = BillingSolutionConstants.SYSTEM_USER_ID,
            subscriptionId = parameters.subscriptionId,
            processingStatus = ProcessingStatus.SUBSCRIPTION_BILLING_PROCESSED,
        )
    }

    private fun calculateNextBillingDate(
        currentBillingDate: LocalDate,
        frequency: com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency,
    ): LocalDate {
        return when (frequency) {
            com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency.DAILY -> currentBillingDate.plusDays(1)
            com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency.WEEKLY -> currentBillingDate.plusWeeks(1)
            com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency.MONTHLY -> currentBillingDate.plusMonths(1)
            com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency.QUARTERLY -> currentBillingDate.plusMonths(3)
            com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionFrequency.YEARLY -> currentBillingDate.plusYears(1)
        }
    }
}
