package com.klosesoft.billingsolution.workflow.steps.subscription

import com.klosesoft.billingsolution.workflow.steps.AbstractWorkflowStep
import org.flowable.engine.delegate.DelegateExecution
import org.springframework.stereotype.Service

@Service
internal class ProcessSubscriptionBilling(private val target: ProcessSubscriptionBillingService) : AbstractWorkflowStep() {

    override suspend fun exec(
        execution: DelegateExecution,
    ) {
        target.exec(execution)
    }
}
